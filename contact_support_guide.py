#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
联系旺店通技术支持指南
关于货品备注获取的具体问题
"""

def create_support_contact_guide():
    """创建技术支持联系指南"""
    
    print("📞 联系旺店通技术支持指南")
    print("=" * 60)
    
    support_info = {
        "问题描述": "无法通过API获取货品档案中的备注信息",
        "账号信息": {
            "货主编号": "AJT-XLSWDT",
            "seller_id": "changhe",
            "appKey": "changhe_chycchsjcjgj_wdt"
        },
        "已使用的接口": "goods.spec.query.step",
        "具体问题": [
            "1. remark字段存在但全部为空",
            "2. spec_prop1-6字段全部为空",
            "3. 所有可能的备注字段都没有内容",
            "4. 需要确认是否有其他接口可以获取备注"
        ],
        "需要确认的问题": [
            "1. 是否有其他API接口可以获取货品备注？",
            "2. 备注信息是否存储在其他字段中？",
            "3. 是否需要特殊权限才能访问备注信息？",
            "4. WMS系统中是否确实设置了商品备注？",
            "5. 是否有商品描述或详情相关的接口？"
        ],
        "联系方式": {
            "官网": "https://www.wdtwms.com/",
            "API文档": "https://www.yuque.com/huice-wiki/bhxv6e",
            "建议": "通过官网客服或技术支持渠道联系"
        }
    }
    
    print("📋 问题详情:")
    print(f"问题: {support_info['问题描述']}")
    print(f"\n账号信息:")
    for key, value in support_info['账号信息'].items():
        print(f"  {key}: {value}")
    
    print(f"\n已使用的接口: {support_info['已使用的接口']}")
    
    print(f"\n具体问题:")
    for problem in support_info['具体问题']:
        print(f"  {problem}")
    
    print(f"\n需要确认的问题:")
    for question in support_info['需要确认的问题']:
        print(f"  {question}")
    
    print(f"\n联系方式:")
    for key, value in support_info['联系方式'].items():
        print(f"  {key}: {value}")
    
    return support_info

if __name__ == "__main__":
    create_support_contact_guide()
