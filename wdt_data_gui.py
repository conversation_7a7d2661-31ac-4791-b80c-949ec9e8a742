#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
旺店通数据自动获取GUI界面
支持定时获取API数据并写入Excel表格
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
from datetime import datetime, timedelta
import json
from wdt_post_client import WDTPostClient

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, archive_file: str = "货品档案.xlsx"):
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            from openpyxl import load_workbook
            
            wb = load_workbook(self.archive_file, read_only=True)
            ws = wb.active
            
            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(str(cell.value).strip())
                else:
                    headers.append('')
            
            # 查找关键字段的索引
            goods_name_idx = None
            goods_no_idx = None
            goods_remark_idx = None
            
            for i, header in enumerate(headers):
                header_lower = header.lower()
                if '货品名称' in header or 'goods_name' in header_lower:
                    goods_name_idx = i
                elif '货品编号' in header or 'goods_no' in header_lower:
                    goods_no_idx = i
                elif '备注' in header or 'remark' in header_lower:
                    goods_remark_idx = i
            
            if goods_name_idx is None:
                goods_name_idx = 0
            
            # 读取数据行
            for row in ws.iter_rows(min_row=2, values_only=True):
                if not row or not any(row):
                    continue
                
                goods_name = ''
                if goods_name_idx < len(row) and row[goods_name_idx]:
                    goods_name = str(row[goods_name_idx]).strip()
                
                if not goods_name:
                    continue
                
                goods_no = ''
                if goods_no_idx is not None and goods_no_idx < len(row) and row[goods_no_idx]:
                    goods_no = str(row[goods_no_idx]).strip()
                
                goods_remark = ''
                if goods_remark_idx is not None and goods_remark_idx < len(row) and row[goods_remark_idx]:
                    goods_remark = str(row[goods_remark_idx]).strip()
                
                self.goods_map[goods_name] = {
                    'goods_no': goods_no,
                    'goods_remark': goods_remark
                }
            
            wb.close()
            return True
            
        except Exception as e:
            return False
    
    def get_goods_info(self, goods_name: str) -> dict:
        """根据货品名称获取货品信息"""
        if not goods_name:
            return {'goods_no': '', 'goods_remark': ''}
        
        # 精确匹配
        if goods_name in self.goods_map:
            return self.goods_map[goods_name]
        
        # 模糊匹配
        goods_name_lower = goods_name.lower().strip()
        for name, info in self.goods_map.items():
            if goods_name_lower in name.lower() or name.lower() in goods_name_lower:
                return info
        
        return {'goods_no': '', 'goods_remark': ''}

class WDTDataGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("旺店通数据自动获取工具")
        self.root.geometry("800x600")
        
        # 初始化变量
        self.is_running = False
        self.timer_thread = None
        self.client = None
        self.goods_mapper = None
        
        # 配置变量
        self.output_path = tk.StringVar(value="C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx")
        self.archive_path = tk.StringVar(value="货品档案.xlsx")
        self.interval_minutes = tk.IntVar(value=60)  # 默认60分钟
        self.auto_start = tk.BooleanVar(value=False)
        
        self.setup_ui()
        self.setup_status()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 输出路径设置
        ttk.Label(config_frame, text="输出文件路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        path_frame = ttk.Frame(config_frame)
        path_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        ttk.Entry(path_frame, textvariable=self.output_path, width=50).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(path_frame, text="浏览", command=self.browse_output_path).grid(row=0, column=1, padx=(5, 0))
        path_frame.columnconfigure(0, weight=1)
        
        # 货品档案路径设置
        ttk.Label(config_frame, text="货品档案路径:").grid(row=1, column=0, sticky=tk.W, pady=2)
        archive_frame = ttk.Frame(config_frame)
        archive_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        ttk.Entry(archive_frame, textvariable=self.archive_path, width=50).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(archive_frame, text="浏览", command=self.browse_archive_path).grid(row=0, column=1, padx=(5, 0))
        archive_frame.columnconfigure(0, weight=1)
        
        # 定时间隔设置
        ttk.Label(config_frame, text="定时间隔(分钟):").grid(row=2, column=0, sticky=tk.W, pady=2)
        interval_frame = ttk.Frame(config_frame)
        interval_frame.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Spinbox(interval_frame, from_=1, to=1440, textvariable=self.interval_minutes, width=10).grid(row=0, column=0)
        ttk.Label(interval_frame, text="分钟 (1-1440分钟)").grid(row=0, column=1, padx=(5, 0))
        
        # 自动启动选项
        ttk.Checkbutton(config_frame, text="程序启动时自动开始定时任务", 
                       variable=self.auto_start).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        config_frame.columnconfigure(1, weight=1)
        
        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="任务控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        self.start_button = ttk.Button(button_frame, text="开始定时任务", command=self.start_timer)
        self.start_button.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_button = ttk.Button(button_frame, text="停止定时任务", command=self.stop_timer, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=5)
        
        self.manual_button = ttk.Button(button_frame, text="立即执行一次", command=self.manual_execute)
        self.manual_button.grid(row=0, column=2, padx=5)
        
        self.test_button = ttk.Button(button_frame, text="测试连接", command=self.test_connection)
        self.test_button.grid(row=0, column=3, padx=(5, 0))
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="状态: 未启动", foreground="red")
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=2)
        
        self.next_run_label = ttk.Label(status_frame, text="下次运行: --")
        self.next_run_label.grid(row=1, column=0, sticky=tk.W, pady=2)
        
        self.last_run_label = ttk.Label(status_frame, text="上次运行: --")
        self.last_run_label.grid(row=2, column=0, sticky=tk.W, pady=2)
        
        self.records_label = ttk.Label(status_frame, text="上次导出记录数: --")
        self.records_label.grid(row=3, column=0, sticky=tk.W, pady=2)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建日志文本框和滚动条
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.log_text = tk.Text(log_text_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 配置主窗口的行列权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def setup_status(self):
        """初始化状态"""
        self.log("程序启动完成")
        self.log("请配置输出路径和货品档案路径")
        
        # 如果设置了自动启动，则自动开始
        if self.auto_start.get():
            self.root.after(1000, self.start_timer)  # 延迟1秒启动
    
    def browse_output_path(self):
        """浏览输出文件路径"""
        filename = filedialog.asksaveasfilename(
            title="选择输出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_path.set(filename)
    
    def browse_archive_path(self):
        """浏览货品档案路径"""
        filename = filedialog.askopenfilename(
            title="选择货品档案Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.archive_path.set(filename)
    
    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def test_connection(self):
        """测试API连接"""
        self.log("正在测试API连接...")
        
        def test_thread():
            try:
                client = WDTPostClient()
                # 简单的API调用测试
                response = client.call_api('stockout.query', {
                    "start_consign_time": datetime.now().strftime('%Y-%m-%d 00:00:00'),
                    "end_consign_time": datetime.now().strftime('%Y-%m-%d 23:59:59'),
                    "page_size": 1,
                    "page_no": 0
                })
                
                if response:
                    self.log("✅ API连接测试成功")
                    messagebox.showinfo("测试结果", "API连接测试成功！")
                else:
                    self.log("❌ API连接测试失败")
                    messagebox.showerror("测试结果", "API连接测试失败！")
                    
            except Exception as e:
                self.log(f"❌ API连接测试失败: {e}")
                messagebox.showerror("测试结果", f"API连接测试失败: {e}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def start_timer(self):
        """开始定时任务"""
        if self.is_running:
            return
        
        # 验证配置
        if not self.output_path.get():
            messagebox.showerror("错误", "请设置输出文件路径")
            return
        
        if not os.path.exists(self.archive_path.get()):
            messagebox.showerror("错误", "货品档案文件不存在")
            return
        
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        self.status_label.config(text="状态: 运行中", foreground="green")
        self.log("定时任务已启动")
        
        # 启动定时器线程
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()
    
    def stop_timer(self):
        """停止定时任务"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.status_label.config(text="状态: 已停止", foreground="red")
        self.next_run_label.config(text="下次运行: --")
        self.log("定时任务已停止")
    
    def timer_worker(self):
        """定时器工作线程"""
        while self.is_running:
            # 执行数据获取任务
            self.execute_data_fetch()
            
            if not self.is_running:
                break
            
            # 计算下次运行时间
            next_run = datetime.now() + timedelta(minutes=self.interval_minutes.get())
            self.next_run_label.config(text=f"下次运行: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 等待指定间隔
            for _ in range(self.interval_minutes.get() * 60):  # 转换为秒
                if not self.is_running:
                    break
                time.sleep(1)
    
    def manual_execute(self):
        """手动执行一次"""
        if not os.path.exists(self.archive_path.get()):
            messagebox.showerror("错误", "货品档案文件不存在")
            return
        
        self.log("开始手动执行数据获取...")
        threading.Thread(target=self.execute_data_fetch, daemon=True).start()
    
    def execute_data_fetch(self):
        """执行数据获取任务"""
        try:
            self.log("开始获取数据...")
            
            # 初始化客户端和映射器
            if not self.client:
                self.client = WDTPostClient()
            
            if not self.goods_mapper:
                self.goods_mapper = GoodsArchiveMapper(self.archive_path.get())
                if not self.goods_mapper.goods_map:
                    self.log("❌ 货品档案加载失败")
                    return
                else:
                    self.log(f"✅ 货品档案加载成功，共 {len(self.goods_mapper.goods_map)} 条记录")
            
            # 执行数据导出（调用原有的导出逻辑）
            result = self.export_data()
            
            if result:
                self.last_run_label.config(text=f"上次运行: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                self.records_label.config(text=f"上次导出记录数: {result}")
                self.log(f"✅ 数据导出完成，共 {result} 条记录")
            else:
                self.log("❌ 数据导出失败")
                
        except Exception as e:
            self.log(f"❌ 执行失败: {e}")
    
    def export_data(self):
        """导出数据的核心逻辑"""
        try:
            # 调用修改后的导出函数
            from export_to_wdt_data import export_to_wdt_data

            # 使用GUI中配置的路径
            result = export_to_wdt_data(
                target_path=self.output_path.get(),
                archive_file=self.archive_path.get(),
                log_callback=self.log
            )

            return result

        except Exception as e:
            self.log(f"导出数据时出错: {e}")
            return None

def main():
    root = tk.Tk()
    app = WDTDataGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
