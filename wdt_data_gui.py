#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
旺店通数据自动获取GUI界面
支持定时获取API数据并写入Excel表格
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
from datetime import datetime, timedelta
import json
from wdt_post_client import WDTPostClient

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, archive_file: str = "货品档案.xlsx"):
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            from openpyxl import load_workbook
            
            wb = load_workbook(self.archive_file, read_only=True)
            ws = wb.active
            
            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(str(cell.value).strip())
                else:
                    headers.append('')
            
            # 查找关键字段的索引
            goods_name_idx = None
            goods_no_idx = None
            goods_remark_idx = None
            
            for i, header in enumerate(headers):
                header_lower = header.lower()
                if '货品名称' in header or 'goods_name' in header_lower:
                    goods_name_idx = i
                elif '货品编号' in header or 'goods_no' in header_lower:
                    goods_no_idx = i
                elif '备注' in header or 'remark' in header_lower:
                    goods_remark_idx = i
            
            if goods_name_idx is None:
                goods_name_idx = 0
            
            # 读取数据行
            for row in ws.iter_rows(min_row=2, values_only=True):
                if not row or not any(row):
                    continue
                
                goods_name = ''
                if goods_name_idx < len(row) and row[goods_name_idx]:
                    goods_name = str(row[goods_name_idx]).strip()
                
                if not goods_name:
                    continue
                
                goods_no = ''
                if goods_no_idx is not None and goods_no_idx < len(row) and row[goods_no_idx]:
                    goods_no = str(row[goods_no_idx]).strip()
                
                goods_remark = ''
                if goods_remark_idx is not None and goods_remark_idx < len(row) and row[goods_remark_idx]:
                    goods_remark = str(row[goods_remark_idx]).strip()
                
                self.goods_map[goods_name] = {
                    'goods_no': goods_no,
                    'goods_remark': goods_remark
                }
            
            wb.close()
            return True
            
        except Exception as e:
            return False
    
    def get_goods_info(self, goods_name: str) -> dict:
        """根据货品名称获取货品信息"""
        if not goods_name:
            return {'goods_no': '', 'goods_remark': ''}
        
        # 精确匹配
        if goods_name in self.goods_map:
            return self.goods_map[goods_name]
        
        # 模糊匹配
        goods_name_lower = goods_name.lower().strip()
        for name, info in self.goods_map.items():
            if goods_name_lower in name.lower() or name.lower() in goods_name_lower:
                return info
        
        return {'goods_no': '', 'goods_remark': ''}

class WDTDataGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("旺店通数据自动获取工具")
        self.root.geometry("800x600")

        # 配置文件路径
        self.config_file = "wdt_gui_config.json"

        # 初始化变量
        self.is_running = False
        self.timer_thread = None
        self.client = None
        self.goods_mapper = None
        
        # 配置变量（先设置默认值）
        self.output_path = tk.StringVar(value="C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx")
        self.archive_path = tk.StringVar(value="货品档案.xlsx")
        self.interval_minutes = tk.IntVar(value=60)  # 默认60分钟
        self.auto_start = tk.BooleanVar(value=True)  # 默认自动启动

        # 定时模式选择
        self.timer_mode = tk.StringVar(value="daily")  # 默认每日定时

        # 每日定时设置
        self.daily_hour = tk.IntVar(value=9)  # 默认上午9点
        self.daily_minute = tk.IntVar(value=0)  # 默认0分

        # 多个每日执行时间
        self.daily_times = []  # 存储多个时间点

        # 加载配置
        self.load_config()

        self.setup_ui()
        self.setup_status()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 输出路径设置
        ttk.Label(config_frame, text="输出文件路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        path_frame = ttk.Frame(config_frame)
        path_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        ttk.Entry(path_frame, textvariable=self.output_path, width=50).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(path_frame, text="浏览", command=self.browse_output_path).grid(row=0, column=1, padx=(5, 0))
        path_frame.columnconfigure(0, weight=1)
        
        # 货品档案路径设置
        ttk.Label(config_frame, text="货品档案路径:").grid(row=1, column=0, sticky=tk.W, pady=2)
        archive_frame = ttk.Frame(config_frame)
        archive_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        ttk.Entry(archive_frame, textvariable=self.archive_path, width=50).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(archive_frame, text="浏览", command=self.browse_archive_path).grid(row=0, column=1, padx=(5, 0))
        archive_frame.columnconfigure(0, weight=1)
        
        # 定时模式选择
        ttk.Label(config_frame, text="定时模式:").grid(row=2, column=0, sticky=tk.W, pady=2)
        mode_frame = ttk.Frame(config_frame)
        mode_frame.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Radiobutton(mode_frame, text="间隔执行", variable=self.timer_mode, value="interval").grid(row=0, column=0)
        ttk.Radiobutton(mode_frame, text="每日定时", variable=self.timer_mode, value="daily").grid(row=0, column=1, padx=(10, 0))

        # 间隔设置
        ttk.Label(config_frame, text="间隔时间(分钟):").grid(row=3, column=0, sticky=tk.W, pady=2)
        interval_frame = ttk.Frame(config_frame)
        interval_frame.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Spinbox(interval_frame, from_=1, to=1440, textvariable=self.interval_minutes, width=10).grid(row=0, column=0)
        ttk.Label(interval_frame, text="分钟 (1-1440分钟)").grid(row=0, column=1, padx=(5, 0))

        # 每日定时设置
        ttk.Label(config_frame, text="每日执行时间:").grid(row=4, column=0, sticky=tk.W, pady=2)
        daily_frame = ttk.Frame(config_frame)
        daily_frame.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 时间选择
        time_select_frame = ttk.Frame(daily_frame)
        time_select_frame.grid(row=0, column=0, sticky=tk.W)
        ttk.Spinbox(time_select_frame, from_=0, to=23, textvariable=self.daily_hour, width=5).grid(row=0, column=0)
        ttk.Label(time_select_frame, text="时").grid(row=0, column=1, padx=(2, 5))
        ttk.Spinbox(time_select_frame, from_=0, to=59, textvariable=self.daily_minute, width=5).grid(row=0, column=2)
        ttk.Label(time_select_frame, text="分").grid(row=0, column=3, padx=(2, 10))

        # 添加时间按钮
        ttk.Button(time_select_frame, text="添加时间", command=self.add_daily_time).grid(row=0, column=4, padx=(5, 0))

        # 时间列表显示
        self.time_list_frame = ttk.Frame(daily_frame)
        self.time_list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        self.update_time_list_display()

        # 自动启动选项
        auto_start_frame = ttk.Frame(config_frame)
        auto_start_frame.grid(row=5, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        self.auto_start_checkbox = ttk.Checkbutton(auto_start_frame, text="程序启动时自动开始定时任务",
                                                  variable=self.auto_start, command=self.on_auto_start_changed)
        self.auto_start_checkbox.grid(row=0, column=0)

        ttk.Button(auto_start_frame, text="保存配置", command=self.save_config_manual).grid(row=0, column=1, padx=(10, 0))
        
        config_frame.columnconfigure(1, weight=1)
        
        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="任务控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        self.start_button = ttk.Button(button_frame, text="开始定时任务", command=self.start_timer)
        self.start_button.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_button = ttk.Button(button_frame, text="停止定时任务", command=self.stop_timer, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=5)
        
        self.manual_button = ttk.Button(button_frame, text="立即执行一次", command=self.manual_execute)
        self.manual_button.grid(row=0, column=2, padx=5)
        
        self.test_button = ttk.Button(button_frame, text="测试连接", command=self.test_connection)
        self.test_button.grid(row=0, column=3, padx=(5, 0))
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="状态: 未启动", foreground="red")
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=2)
        
        self.mode_label = ttk.Label(status_frame, text="执行模式: --")
        self.mode_label.grid(row=1, column=0, sticky=tk.W, pady=2)

        self.next_run_label = ttk.Label(status_frame, text="下次运行: --")
        self.next_run_label.grid(row=2, column=0, sticky=tk.W, pady=2)

        self.last_run_label = ttk.Label(status_frame, text="上次运行: --")
        self.last_run_label.grid(row=3, column=0, sticky=tk.W, pady=2)

        self.records_label = ttk.Label(status_frame, text="上次导出记录数: --")
        self.records_label.grid(row=4, column=0, sticky=tk.W, pady=2)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建日志文本框和滚动条
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.log_text = tk.Text(log_text_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 配置主窗口的行列权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def setup_status(self):
        """初始化状态"""
        # 更新时间列表显示（配置已在load_config中加载）
        self.update_time_list_display()

        self.log("程序启动完成")
        self.log(f"配置文件: {self.config_file}")
        self.log(f"输出路径: {self.output_path.get()}")
        self.log(f"货品档案: {self.archive_path.get()}")

        if self.timer_mode.get() == "daily":
            self.log(f"每日执行时间: {', '.join(self.daily_times)}")
        else:
            self.log(f"间隔执行: 每{self.interval_minutes.get()}分钟")

        # 如果设置了自动启动，则自动开始
        if self.auto_start.get():
            self.log("⏰ 自动启动已启用，3秒后开始定时任务...")
            self.root.after(3000, self.auto_start_timer)  # 延迟3秒启动
        else:
            self.log("💡 提示: 可在配置中启用自动启动功能")

    def auto_start_timer(self):
        """自动启动定时任务"""
        try:
            # 验证基本配置
            if not self.output_path.get():
                self.log("❌ 自动启动失败: 未设置输出文件路径")
                return

            if not os.path.exists(self.archive_path.get()):
                self.log(f"❌ 自动启动失败: 货品档案文件不存在 - {self.archive_path.get()}")
                return

            if self.timer_mode.get() == "daily" and not self.daily_times:
                self.log("❌ 自动启动失败: 每日定时模式需要至少设置一个执行时间")
                return

            # 启动定时任务
            self.start_timer()
            self.log("🚀 自动启动成功！")

        except Exception as e:
            self.log(f"❌ 自动启动失败: {e}")
    
    def browse_output_path(self):
        """浏览输出文件路径"""
        filename = filedialog.asksaveasfilename(
            title="选择输出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_path.set(filename)
            self.log(f"输出路径已更新: {filename}")
            # 自动保存配置
            self.save_config()
    
    def browse_archive_path(self):
        """浏览货品档案路径"""
        filename = filedialog.askopenfilename(
            title="选择货品档案Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.archive_path.set(filename)
            self.log(f"货品档案路径已更新: {filename}")
            # 自动保存配置
            self.save_config()
    
    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载配置到变量
                self.output_path.set(config.get('output_path', self.output_path.get()))
                self.archive_path.set(config.get('archive_path', self.archive_path.get()))
                self.interval_minutes.set(config.get('interval_minutes', self.interval_minutes.get()))
                self.auto_start.set(config.get('auto_start', self.auto_start.get()))
                self.timer_mode.set(config.get('timer_mode', self.timer_mode.get()))
                self.daily_hour.set(config.get('daily_hour', self.daily_hour.get()))
                self.daily_minute.set(config.get('daily_minute', self.daily_minute.get()))
                self.daily_times = config.get('daily_times', [])

                print(f"✅ 配置加载成功: {self.config_file}")
            else:
                # 如果配置文件不存在，使用默认配置
                self.daily_times = ["09:00", "14:00", "18:00"]
                print("📋 使用默认配置")

        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            # 使用默认配置
            self.daily_times = ["09:00", "14:00", "18:00"]

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'output_path': self.output_path.get(),
                'archive_path': self.archive_path.get(),
                'interval_minutes': self.interval_minutes.get(),
                'auto_start': self.auto_start.get(),
                'timer_mode': self.timer_mode.get(),
                'daily_hour': self.daily_hour.get(),
                'daily_minute': self.daily_minute.get(),
                'daily_times': self.daily_times,
                'last_saved': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"✅ 配置保存成功: {self.config_file}")

        except Exception as e:
            print(f"❌ 配置保存失败: {e}")

    def on_auto_start_changed(self):
        """自动启动选项变化时的处理"""
        self.log(f"自动启动设置: {'已启用' if self.auto_start.get() else '已禁用'}")
        # 自动保存配置
        self.save_config()

    def save_config_manual(self):
        """手动保存配置"""
        self.save_config()
        self.log("✅ 配置已手动保存")
        messagebox.showinfo("提示", "配置保存成功！")

    def on_closing(self):
        """窗口关闭事件处理"""
        # 停止定时任务
        if self.is_running:
            self.stop_timer()

        # 保存配置
        self.save_config()
        self.log("程序正在关闭...")

        # 关闭窗口
        self.root.destroy()

    def add_daily_time(self):
        """添加每日执行时间"""
        hour = self.daily_hour.get()
        minute = self.daily_minute.get()
        time_str = f"{hour:02d}:{minute:02d}"

        # 检查是否已存在
        if time_str not in self.daily_times:
            self.daily_times.append(time_str)
            self.daily_times.sort()  # 按时间排序
            self.update_time_list_display()
            self.log(f"添加每日执行时间: {time_str}")
            # 自动保存配置
            self.save_config()
        else:
            messagebox.showwarning("警告", f"时间 {time_str} 已存在")

    def remove_daily_time(self, time_str):
        """删除每日执行时间"""
        if time_str in self.daily_times:
            self.daily_times.remove(time_str)
            self.update_time_list_display()
            self.log(f"删除每日执行时间: {time_str}")
            # 自动保存配置
            self.save_config()

    def update_time_list_display(self):
        """更新时间列表显示"""
        # 清空现有显示
        for widget in self.time_list_frame.winfo_children():
            widget.destroy()

        if not self.daily_times:
            ttk.Label(self.time_list_frame, text="未设置执行时间", foreground="gray").grid(row=0, column=0)
            return

        # 显示时间列表
        ttk.Label(self.time_list_frame, text="已设置的执行时间:", font=("", 9, "bold")).grid(row=0, column=0, sticky=tk.W)

        for i, time_str in enumerate(self.daily_times):
            time_frame = ttk.Frame(self.time_list_frame)
            time_frame.grid(row=i+1, column=0, sticky=tk.W, pady=1)

            ttk.Label(time_frame, text=f"• {time_str}").grid(row=0, column=0)
            ttk.Button(time_frame, text="删除",
                      command=lambda t=time_str: self.remove_daily_time(t),
                      width=6).grid(row=0, column=1, padx=(10, 0))
    
    def test_connection(self):
        """测试API连接"""
        self.log("正在测试API连接...")
        
        def test_thread():
            try:
                client = WDTPostClient()
                # 简单的API调用测试
                response = client.call_api('stockout.query', {
                    "start_consign_time": datetime.now().strftime('%Y-%m-%d 00:00:00'),
                    "end_consign_time": datetime.now().strftime('%Y-%m-%d 23:59:59'),
                    "page_size": 1,
                    "page_no": 0
                })
                
                if response:
                    self.log("✅ API连接测试成功")
                    messagebox.showinfo("测试结果", "API连接测试成功！")
                else:
                    self.log("❌ API连接测试失败")
                    messagebox.showerror("测试结果", "API连接测试失败！")
                    
            except Exception as e:
                self.log(f"❌ API连接测试失败: {e}")
                messagebox.showerror("测试结果", f"API连接测试失败: {e}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def start_timer(self):
        """开始定时任务"""
        if self.is_running:
            return

        # 验证配置
        if not self.output_path.get():
            messagebox.showerror("错误", "请设置输出文件路径")
            return

        if not os.path.exists(self.archive_path.get()):
            messagebox.showerror("错误", "货品档案文件不存在")
            return

        # 验证每日定时模式的配置
        if self.timer_mode.get() == "daily" and not self.daily_times:
            messagebox.showerror("错误", "每日定时模式需要至少设置一个执行时间")
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        mode_text = "间隔执行" if self.timer_mode.get() == "interval" else "每日定时"
        self.status_label.config(text=f"状态: 运行中", foreground="green")
        self.mode_label.config(text=f"执行模式: {mode_text}")

        if self.timer_mode.get() == "interval":
            self.log(f"定时任务已启动 - {mode_text} (每{self.interval_minutes.get()}分钟)")
        else:
            times_str = ", ".join(self.daily_times)
            self.log(f"定时任务已启动 - {mode_text} ({times_str})")

        # 启动定时器线程
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()
    
    def stop_timer(self):
        """停止定时任务"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

        self.status_label.config(text="状态: 已停止", foreground="red")
        self.mode_label.config(text="执行模式: --")
        self.next_run_label.config(text="下次运行: --")
        self.log("定时任务已停止")
    
    def timer_worker(self):
        """定时器工作线程"""
        if self.timer_mode.get() == "interval":
            self.interval_timer_worker()
        else:
            self.daily_timer_worker()

    def interval_timer_worker(self):
        """间隔执行模式的定时器"""
        while self.is_running:
            # 执行数据获取任务
            self.execute_data_fetch()

            if not self.is_running:
                break

            # 计算下次运行时间
            next_run = datetime.now() + timedelta(minutes=self.interval_minutes.get())
            self.next_run_label.config(text=f"下次运行: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")

            # 等待指定间隔
            for _ in range(self.interval_minutes.get() * 60):  # 转换为秒
                if not self.is_running:
                    break
                time.sleep(1)

    def daily_timer_worker(self):
        """每日定时执行模式的定时器"""
        while self.is_running:
            now = datetime.now()
            next_run_time = self.get_next_daily_run_time()

            if next_run_time:
                self.next_run_label.config(text=f"下次运行: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 计算等待时间
                wait_seconds = (next_run_time - now).total_seconds()

                if wait_seconds > 0:
                    # 等待到执行时间
                    for _ in range(int(wait_seconds)):
                        if not self.is_running:
                            break
                        time.sleep(1)

                        # 每分钟更新一次显示
                        if _ % 60 == 0:
                            remaining = wait_seconds - _
                            if remaining > 3600:
                                self.log(f"距离下次执行还有 {remaining/3600:.1f} 小时")
                            elif remaining > 60:
                                self.log(f"距离下次执行还有 {remaining/60:.0f} 分钟")

                if self.is_running:
                    # 执行数据获取任务
                    self.execute_data_fetch()
            else:
                # 如果没有设置时间，等待1分钟后重新检查
                time.sleep(60)

    def get_next_daily_run_time(self):
        """获取下次每日执行时间"""
        if not self.daily_times:
            return None

        now = datetime.now()
        today = now.date()

        # 检查今天是否还有未执行的时间
        for time_str in self.daily_times:
            hour, minute = map(int, time_str.split(':'))
            run_time = datetime.combine(today, datetime.min.time().replace(hour=hour, minute=minute))

            if run_time > now:
                return run_time

        # 如果今天没有了，返回明天的第一个时间
        tomorrow = today + timedelta(days=1)
        first_time = self.daily_times[0]
        hour, minute = map(int, first_time.split(':'))
        return datetime.combine(tomorrow, datetime.min.time().replace(hour=hour, minute=minute))
    
    def manual_execute(self):
        """手动执行一次"""
        if not os.path.exists(self.archive_path.get()):
            messagebox.showerror("错误", "货品档案文件不存在")
            return
        
        self.log("开始手动执行数据获取...")
        threading.Thread(target=self.execute_data_fetch, daemon=True).start()
    
    def execute_data_fetch(self):
        """执行数据获取任务"""
        try:
            self.log("开始获取数据...")
            
            # 初始化客户端和映射器
            if not self.client:
                self.client = WDTPostClient()
            
            if not self.goods_mapper:
                self.goods_mapper = GoodsArchiveMapper(self.archive_path.get())
                if not self.goods_mapper.goods_map:
                    self.log("❌ 货品档案加载失败")
                    return
                else:
                    self.log(f"✅ 货品档案加载成功，共 {len(self.goods_mapper.goods_map)} 条记录")
            
            # 执行数据导出（调用原有的导出逻辑）
            result = self.export_data()
            
            if result:
                self.last_run_label.config(text=f"上次运行: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                self.records_label.config(text=f"上次导出记录数: {result}")
                self.log(f"✅ 数据导出完成，共 {result} 条记录")
            else:
                self.log("❌ 数据导出失败")
                
        except Exception as e:
            self.log(f"❌ 执行失败: {e}")
    
    def export_data(self):
        """导出数据的核心逻辑"""
        try:
            # 调用修改后的导出函数
            from export_to_wdt_data import export_to_wdt_data

            # 使用GUI中配置的路径
            result = export_to_wdt_data(
                target_path=self.output_path.get(),
                archive_file=self.archive_path.get(),
                log_callback=self.log
            )

            return result

        except Exception as e:
            self.log(f"导出数据时出错: {e}")
            return None

def main():
    root = tk.Tk()
    app = WDTDataGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
