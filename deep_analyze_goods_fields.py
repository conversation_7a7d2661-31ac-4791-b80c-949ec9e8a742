#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
深度分析货品档案的所有字段
查找可能包含备注信息的字段
"""

import json
import os
from datetime import datetime

def deep_analyze_goods_fields():
    """深度分析货品档案的所有字段"""
    
    print("🔍 深度分析货品档案的所有字段")
    print("=" * 60)
    
    # 加载货品档案
    try:
        with open('complete_goods_archive_20250711_104745.json', 'r', encoding='utf-8') as f:
            goods_archive = json.load(f)
    except:
        print("❌ 无法加载货品档案文件")
        return
    
    print(f"📦 分析 {len(goods_archive)} 个商品的字段...")
    
    # 收集所有字段和它们的值
    all_fields = {}
    non_empty_fields = {}
    
    for spec_no, item in goods_archive.items():
        full_data = item.get('full_data', {})
        if isinstance(full_data, dict):
            for field_name, field_value in full_data.items():
                # 记录所有字段
                if field_name not in all_fields:
                    all_fields[field_name] = []
                all_fields[field_name].append(str(field_value))
                
                # 记录非空字段
                if field_value and str(field_value).strip():
                    if field_name not in non_empty_fields:
                        non_empty_fields[field_name] = []
                    non_empty_fields[field_name].append({
                        'spec_no': spec_no,
                        'goods_name': item.get('goods_name', ''),
                        'value': str(field_value).strip()
                    })
    
    print(f"\n📊 字段统计:")
    print(f"📋 总字段数: {len(all_fields)}")
    print(f"📝 有内容的字段数: {len(non_empty_fields)}")
    
    # 显示所有字段及其状态
    print(f"\n📋 所有字段列表:")
    for field_name in sorted(all_fields.keys()):
        unique_values = set(all_fields[field_name])
        non_empty_count = len([v for v in unique_values if v.strip()])
        
        status = "✅ 有内容" if non_empty_count > 0 else "❌ 全部为空"
        print(f"  {field_name}: {status} (非空值数量: {non_empty_count})")
    
    # 重点分析可能包含备注的字段
    potential_remark_fields = []
    for field_name in all_fields.keys():
        field_lower = field_name.lower()
        if any(keyword in field_lower for keyword in [
            'remark', 'note', 'memo', 'desc', 'comment', 'info', 
            'detail', 'explain', 'instruction', 'spec', 'prop'
        ]):
            potential_remark_fields.append(field_name)
    
    print(f"\n🔍 可能包含备注的字段:")
    for field_name in potential_remark_fields:
        if field_name in non_empty_fields:
            print(f"  ✅ {field_name}: 有 {len(non_empty_fields[field_name])} 个商品有内容")
            # 显示一些示例
            for i, example in enumerate(non_empty_fields[field_name][:3]):
                print(f"    示例 {i+1}: {example['goods_name']} = {example['value']}")
        else:
            print(f"  ❌ {field_name}: 全部为空")
    
    # 分析所有有内容的字段
    print(f"\n📝 所有有内容的字段详情:")
    for field_name, examples in non_empty_fields.items():
        print(f"\n🔍 字段: {field_name}")
        print(f"  📊 有内容的商品数: {len(examples)}")
        
        # 显示一些示例值
        print(f"  📋 示例值:")
        for i, example in enumerate(examples[:5]):
            value = example['value']
            if len(value) > 100:
                value = value[:100] + "..."
            print(f"    {i+1}. {example['goods_name']}: {value}")
        
        if len(examples) > 5:
            print(f"    ... 还有 {len(examples) - 5} 个商品")
    
    # 特别检查可能被忽略的字段
    print(f"\n🔍 特别检查可能的备注字段:")
    special_check_fields = [
        'goods_desc', 'product_desc', 'item_desc', 'description',
        'goods_info', 'product_info', 'item_info', 'info',
        'goods_detail', 'product_detail', 'item_detail',
        'goods_note', 'product_note', 'item_note',
        'goods_memo', 'product_memo', 'item_memo',
        'goods_comment', 'product_comment', 'item_comment',
        'spec_desc', 'spec_info', 'spec_detail', 'spec_note',
        'custom_field1', 'custom_field2', 'custom_field3',
        'extend_field1', 'extend_field2', 'extend_field3',
        'user_field1', 'user_field2', 'user_field3'
    ]
    
    found_special = False
    for field_name in special_check_fields:
        if field_name in all_fields:
            if field_name in non_empty_fields:
                print(f"  ✅ 找到 {field_name}: 有内容")
                found_special = True
            else:
                print(f"  ❌ 找到 {field_name}: 但为空")
    
    if not found_special:
        print(f"  📝 没有找到常见的备注字段名")
    
    # 导出详细分析结果
    analysis_result = {
        'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_goods': len(goods_archive),
        'total_fields': len(all_fields),
        'non_empty_fields_count': len(non_empty_fields),
        'all_fields': {field: len(set(values)) for field, values in all_fields.items()},
        'non_empty_fields': {field: len(examples) for field, examples in non_empty_fields.items()},
        'potential_remark_fields': potential_remark_fields,
        'detailed_non_empty_fields': non_empty_fields
    }
    
    analysis_filename = f"goods_fields_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(analysis_filename, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 详细分析结果已保存到: {analysis_filename}")
    
    # 总结建议
    print(f"\n💡 分析总结和建议:")
    if len(non_empty_fields) == 0:
        print(f"❌ 所有字段都为空，可能的原因:")
        print(f"   1. WMS系统中确实没有设置任何备注或描述信息")
        print(f"   2. 备注信息可能在其他接口或系统中")
        print(f"   3. 需要特殊权限才能访问备注信息")
        print(f"   4. 备注信息可能在商品主数据的其他表中")
    else:
        print(f"✅ 找到 {len(non_empty_fields)} 个有内容的字段")
        print(f"📝 建议重点关注这些字段，它们可能包含有用信息")
    
    print(f"\n🔧 建议的下一步行动:")
    print(f"1. 联系旺店通技术支持，询问备注信息的正确获取方式")
    print(f"2. 检查WMS系统界面，确认备注信息是否真的存在")
    print(f"3. 询问是否有其他API接口可以获取商品描述或备注")
    print(f"4. 确认账号权限是否足够访问所有商品信息")
    
    return analysis_result

if __name__ == "__main__":
    deep_analyze_goods_fields()
