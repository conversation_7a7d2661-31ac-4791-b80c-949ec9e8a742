#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出已取消和已发货的销售出库明细（扩展版）
支持自定义时间范围，确保能找到已取消的订单
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient

class SimpleGoodsArchiveMapper:
    """简化版货品档案映射器（仅使用openpyxl）"""
    
    def __init__(self, archive_file: str = "货品档案.xlsx"):
        """
        初始化货品档案映射器
        
        Args:
            archive_file: 货品档案Excel文件路径
        """
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            from openpyxl import load_workbook
            
            print(f"📋 正在加载货品档案: {self.archive_file}")
            
            # 读取Excel文件
            wb = load_workbook(self.archive_file, read_only=True)
            ws = wb.active
            
            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(str(cell.value).strip())
                else:
                    headers.append('')
            
            print(f"📋 字段列表: {headers}")
            
            # 查找关键字段的索引
            goods_name_idx = None
            goods_no_idx = None
            goods_remark_idx = None
            
            # 尝试不同的字段名称组合
            goods_name_fields = ['货品名称', '商品名称', '产品名称', 'goods_name', 'product_name']
            goods_no_fields = ['货品编号', '商品编号', '产品编号', 'goods_no', 'product_no', 'goods_code']
            goods_remark_fields = ['货品备注', '商品备注', '产品备注', 'goods_remark', 'product_remark', 'remark', '备注']
            
            for i, header in enumerate(headers):
                header_lower = header.lower()
                if goods_name_idx is None:
                    for field in goods_name_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_name_idx = i
                            print(f"✅ 找到货品名称字段: {header} (列 {i+1})")
                            break
                
                if goods_no_idx is None:
                    for field in goods_no_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_no_idx = i
                            print(f"✅ 找到货品编号字段: {header} (列 {i+1})")
                            break
                
                if goods_remark_idx is None:
                    for field in goods_remark_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_remark_idx = i
                            print(f"✅ 找到货品备注字段: {header} (列 {i+1})")
                            break
            
            if goods_name_idx is None:
                print("⚠️ 未找到货品名称字段，将使用第一列作为货品名称")
                goods_name_idx = 0
            
            # 读取数据行
            row_count = 0
            for row in ws.iter_rows(min_row=2, values_only=True):
                if not row or not any(row):
                    continue
                
                # 获取货品名称
                goods_name = ''
                if goods_name_idx < len(row) and row[goods_name_idx]:
                    goods_name = str(row[goods_name_idx]).strip()
                
                if not goods_name:
                    continue
                
                # 获取货品编号
                goods_no = ''
                if goods_no_idx is not None and goods_no_idx < len(row) and row[goods_no_idx]:
                    goods_no = str(row[goods_no_idx]).strip()
                
                # 获取货品备注
                goods_remark = ''
                if goods_remark_idx is not None and goods_remark_idx < len(row) and row[goods_remark_idx]:
                    goods_remark = str(row[goods_remark_idx]).strip()
                
                self.goods_map[goods_name] = {
                    'goods_no': goods_no,
                    'goods_remark': goods_remark
                }
                row_count += 1
            
            wb.close()
            
            print(f"✅ 成功加载 {len(self.goods_map)} 条货品映射")
            
            # 显示前几条映射示例
            if self.goods_map:
                print(f"📋 映射示例:")
                for i, (name, info) in enumerate(list(self.goods_map.items())[:3]):
                    print(f"  {i+1}. {name} -> 编号: {info['goods_no']}, 备注: {info['goods_remark'][:50]}...")
            
        except FileNotFoundError:
            print(f"❌ 货品档案文件未找到: {self.archive_file}")
            print("请确保文件存在于当前目录")
        except ImportError:
            print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
    
    def get_goods_info(self, goods_name: str) -> Dict[str, str]:
        """
        根据货品名称获取货品信息
        
        Args:
            goods_name: 货品名称
            
        Returns:
            包含货品编号和备注的字典
        """
        if not goods_name:
            return {'goods_no': '', 'goods_remark': ''}
        
        # 精确匹配
        if goods_name in self.goods_map:
            return self.goods_map[goods_name]
        
        # 模糊匹配
        goods_name_lower = goods_name.lower().strip()
        for name, info in self.goods_map.items():
            if goods_name_lower in name.lower() or name.lower() in goods_name_lower:
                return info
        
        return {'goods_no': '', 'goods_remark': ''}

def export_cancelled_shipped_extended(days_back: int = 7):
    """导出指定天数内已取消和已发货的销售出库明细（扩展版）"""

    print("🚛 导出已取消和已发货的销售出库明细（扩展版）")
    print("=" * 60)

    # 初始化货品档案映射器
    goods_mapper = SimpleGoodsArchiveMapper()

    # 创建客户端
    client = WDTPostClient()

    # 设置时间范围 - 按天分段查询（API限制单次查询不能超过1天）
    today = datetime.now()

    print(f"📅 查询日期范围: 过去{days_back}天")
    print(f"⚠️ 注意: API限制单次查询不能超过1天，将按天分段查询")

    print(f"🔍 开始分天查询出库单...")

    all_stockouts = []
    total_cancelled = 0
    total_shipped = 0

    # 按天循环查询
    for day_offset in range(days_back):
        query_date = today - timedelta(days=day_offset)
        start_time = query_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = query_date.replace(hour=23, minute=59, second=59, microsecond=0)

        # 如果是今天，结束时间不能超过当前时间
        if query_date.date() == today.date():
            if end_time > datetime.now():
                end_time = datetime.now()

        print(f"\n📅 查询第{day_offset + 1}天: {query_date.strftime('%Y-%m-%d')}")
        print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")

        # 查询参数
        base_params = {
            "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "page_size": 50
        }

        # 查询当天的数据
        day_stockouts = []
        day_cancelled = 0
        day_shipped = 0
        page_no = 0

        # 首先获取当天总记录数
        try:
            first_response = client.call_api('stockout.query', base_params)
            day_total = int(first_response.get('total', 0))
            print(f"📊 当天总记录数: {day_total}")
        except Exception as e:
            print(f"❌ 获取当天总记录数失败: {e}")
            continue  # 跳过这一天，继续查询下一天

        # 如果当天没有数据，跳过
        if day_total == 0:
            print(f"📝 {query_date.strftime('%Y-%m-%d')} 无数据")
            continue

        # 分页查询当天数据
        while True:
            try:
                # 当前页参数
                current_params = {
                    **base_params,
                    "page_no": page_no
                }

                # 调用API
                response = client.call_api('stockout.query', current_params)

                if not response:
                    break

                # 获取数据
                content = response.get('content', [])

                # 处理数据
                page_data = []
                if isinstance(content, list):
                    page_data = content
                elif isinstance(content, dict):
                    page_data = content.get('content', [])
                    if not isinstance(page_data, list):
                        page_data = [page_data] if page_data else []

                if not page_data:
                    break

                # 筛选已取消(status=5)和已发货(status=95)的数据
                filtered_data = []
                status_count = {}
                cancelled_count = 0
                shipped_count = 0

                for item in page_data:
                    if isinstance(item, dict):
                        status = str(item.get('status', ''))
                        # 统计所有状态
                        status_count[status] = status_count.get(status, 0) + 1

                        if status in ['5', '95']:  # 已取消或已发货状态
                            filtered_data.append(item)
                            if status == '5':
                                cancelled_count += 1
                            elif status == '95':
                                shipped_count += 1

                print(f"   📄 第{page_no + 1}页: 总数据 {len(page_data)} 条, 已取消 {cancelled_count} 条, 已发货 {shipped_count} 条")

                # 显示状态分布（仅在第一页显示）
                if page_no == 0 and day_offset == 0:
                    print(f"   📊 状态分布示例: {status_count}")

                if filtered_data:
                    day_stockouts.extend(filtered_data)
                    day_cancelled += cancelled_count
                    day_shipped += shipped_count

                # 检查是否还有更多数据
                if len(page_data) == 0 or len(page_data) < base_params["page_size"]:
                    break

                page_no += 1

                # 安全限制：最多查询100页
                if page_no >= 100:
                    break

            except Exception as e:
                print(f"   ❌ 第{page_no + 1}页查询失败: {e}")
                break

        # 当天查询完成
        if day_stockouts:
            all_stockouts.extend(day_stockouts)
            total_cancelled += day_cancelled
            total_shipped += day_shipped
            print(f"   ✅ {query_date.strftime('%Y-%m-%d')} 完成: 已取消 {day_cancelled} 条, 已发货 {day_shipped} 条")
        else:
            print(f"   📝 {query_date.strftime('%Y-%m-%d')} 无符合条件的数据")
    
    print(f"\n📊 查询完成！")
    print(f"📈 总计获取: 已取消 {total_cancelled} 条, 已发货 {total_shipped} 条, 总计 {len(all_stockouts)} 条")

    if not all_stockouts:
        print("📝 指定时间范围内没有已取消或已发货的订单")
        return

    if total_cancelled == 0:
        print("⚠️ 注意：没有找到已取消的订单，可能需要扩大查询时间范围")
        print("💡 建议：可以增加 days_back 参数来查询更长时间范围")
    else:
        print(f"🎉 找到了 {total_cancelled} 条已取消的订单！")

    # 导出到Excel
    import time
    timestamp = int(time.time())
    start_date = (today - timedelta(days=days_back)).strftime('%Y%m%d')
    end_date = today.strftime('%Y%m%d')
    filename = f"销售出库明细_扩展版_{start_date}_{end_date}_{timestamp}.xlsx"

    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")

        # 创建工作簿
        wb = Workbook()

        # 创建主表
        ws_main = wb.active
        ws_main.title = "销售出库明细"

        # 定义表头
        headers = [
            '出库单号', '原始订单号', '交易单号', '原始交易号',
            '货主编号', '货主名称', '仓库编号', '仓库名称', '店铺名称', '店铺编号',
            '物流编码', '物流名称', '物流单号', '快递单号',
            '买家昵称', '收件人姓名', '收件人手机', '收件人电话',
            '收件人省份', '收件人城市', '收件人区县', '收件人地址', '收件人区域',
            '订单总金额', '货品数量', '货品种类数', '重量', '体积', '计算重量',
            '备注', '客服备注', '买家留言', '标记名称', '打印备注',
            '发货时间', '创建时间', '修改时间', '状态', '状态名称',
            '交易时间', '付款时间', '平台名称', '交易确认时间', '交易创建时间',
            '拣货单号', '拣货类型', '包装评分', '拣货评分', '检验评分',
            '操作员', '检验员', '打印员', '拣货员', '检验员名称', '包装员',
            # 商品明细字段
            '商品序号', '规格编号', '规格名称', '货品名称', '货品编号', '货品备注',
            '商品数量', '商品毛重', '商品净重', '商品体积', '商品备注', '条形码',
            '单位比例', '订单价格', '商品总金额', '商品图片'
        ]

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # 写入数据
        row = 2
        matched_count = 0
        total_goods_count = 0

        for stockout in all_stockouts:
            # 获取状态名称
            status = str(stockout.get('status', ''))
            status_name = '已取消' if status == '5' else '已发货' if status == '95' else f'状态{status}'

            # 基础出库单信息
            base_data = [
                stockout.get('stockout_no', ''),
                stockout.get('src_order_no', ''),
                stockout.get('trade_no', ''),
                stockout.get('src_tids', ''),
                stockout.get('owner_no', ''),
                stockout.get('owner_name', ''),  # 货主名称
                stockout.get('warehouse_no', ''),
                stockout.get('warehouse_name', ''),
                stockout.get('shop_name', ''),
                stockout.get('shop_no', ''),
                stockout.get('logistics_code', ''),
                stockout.get('logistics_name', ''),
                stockout.get('logistics_no', ''),
                stockout.get('express_no', ''),
                stockout.get('buyer_nick', ''),
                stockout.get('receiver_name', ''),
                stockout.get('receiver_mobile', ''),
                stockout.get('receiver_telno', ''),
                stockout.get('receiver_province', ''),
                stockout.get('receiver_city', ''),
                stockout.get('receiver_district', ''),
                stockout.get('receiver_address', ''),
                stockout.get('receiver_area', ''),  # 收件人区域
                stockout.get('total_amount', ''),
                stockout.get('goods_count', ''),
                stockout.get('goods_type_count', ''),
                stockout.get('weight', ''),
                stockout.get('volume', ''),
                stockout.get('calc_weight', ''),  # 计算重量
                stockout.get('remark', ''),
                stockout.get('cs_remark', ''),
                stockout.get('buyer_message', ''),
                stockout.get('flag_name', ''),
                stockout.get('print_remark', ''),  # 打印备注
                stockout.get('consign_time', ''),
                stockout.get('created', ''),
                stockout.get('modified', ''),
                stockout.get('status', ''),
                status_name,
                stockout.get('trade_time', ''),
                stockout.get('pay_time', ''),
                stockout.get('platform_name', ''),
                stockout.get('trade_check_time', ''),  # 交易确认时间
                stockout.get('trade_create_time', ''),  # 交易创建时间
                stockout.get('picklist_no', ''),  # 拣货单号
                stockout.get('pick_type', ''),  # 拣货类型
                stockout.get('pack_score', ''),  # 包装评分
                stockout.get('pick_score', ''),  # 拣货评分
                stockout.get('examine_score', ''),  # 检验评分
                stockout.get('operator_name', ''),  # 操作员
                stockout.get('checker_name', ''),  # 检验员
                stockout.get('printer_name', ''),  # 打印员
                stockout.get('picker_name', ''),  # 拣货员
                stockout.get('examiner_name', ''),  # 检验员名称
                stockout.get('packer_name', '')  # 包装员
            ]

            # 商品明细
            details_list = stockout.get('goods_detail', [])
            if details_list:
                for i, detail in enumerate(details_list):
                    total_goods_count += 1

                    # 获取货品名称
                    goods_name = detail.get('goods_name', '')

                    # 从货品档案中查找货品编号和备注
                    goods_info = goods_mapper.get_goods_info(goods_name)

                    # 统计匹配情况
                    if goods_info['goods_no'] or goods_info['goods_remark']:
                        matched_count += 1

                    goods_data = [
                        i + 1,  # 商品序号
                        detail.get('spec_no', ''),
                        detail.get('spec_name', ''),  # 规格名称
                        goods_name,
                        goods_info['goods_no'],  # 从档案中获取的货品编号
                        goods_info['goods_remark'],  # 从档案中获取的货品备注
                        detail.get('num', ''),
                        detail.get('gross_weight', ''),  # 商品毛重
                        detail.get('net_weight', ''),  # 商品净重
                        detail.get('volume', ''),
                        detail.get('remark', ''),
                        detail.get('barcode', ''),  # 条形码
                        detail.get('unit_ratio', ''),  # 单位比例
                        detail.get('order_price', ''),  # 订单价格
                        detail.get('goods_total_amount', ''),  # 商品总金额
                        detail.get('img_url', '')  # 商品图片
                    ]

                    # 写入完整行数据
                    full_row_data = base_data + goods_data
                    for col, value in enumerate(full_row_data, 1):
                        ws_main.cell(row=row, column=col, value=str(value))
                    row += 1
            else:
                # 没有商品明细的出库单，商品相关字段留空
                goods_data = ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']  # 16个字段
                full_row_data = base_data + goods_data
                for col, value in enumerate(full_row_data, 1):
                    ws_main.cell(row=row, column=col, value=str(value))
                row += 1

        # 自动调整列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width

        # 创建汇总表
        ws_summary = wb.create_sheet("数据汇总")

        # 汇总表头
        summary_headers = ['统计项目', '数值', '说明']
        for col, header in enumerate(summary_headers, 1):
            cell = ws_summary.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # 统计数据
        match_rate = (matched_count / total_goods_count * 100) if total_goods_count > 0 else 0

        # 写入汇总数据
        start_date_str = (today - timedelta(days=days_back)).strftime('%Y-%m-%d')
        end_date_str = today.strftime('%Y-%m-%d')

        summary_data = [
            ('查询开始日期', start_date_str, '数据查询的开始日期'),
            ('查询结束日期', end_date_str, '数据查询的结束日期'),
            ('查询天数', days_back, '查询的时间跨度（天）'),
            ('导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'),
            ('已取消订单数', total_cancelled, '状态为5的出库单数量'),
            ('已发货订单数', total_shipped, '状态为95的出库单数量'),
            ('总出库单数', len(all_stockouts), '已取消和已发货的出库单总数'),
            ('总商品明细数', total_goods_count, '所有商品明细条目数量'),
            ('档案匹配数', matched_count, '成功匹配到货品档案的商品数量'),
            ('匹配率', f'{match_rate:.1f}%', '货品档案匹配成功率'),
            ('货品档案总数', len(goods_mapper.goods_map), '货品档案中的总记录数')
        ]

        for row_idx, (item, value, desc) in enumerate(summary_data, 2):
            ws_summary.cell(row=row_idx, column=1, value=item)
            ws_summary.cell(row=row_idx, column=2, value=str(value))
            ws_summary.cell(row=row_idx, column=3, value=desc)

        # 保存文件
        wb.save(filename)

        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_stockouts)} 条出库单")
        print(f"📦 商品明细: {total_goods_count} 条")
        print(f"🎯 档案匹配: {matched_count} 条 ({match_rate:.1f}%)")
        print(f"📁 文件路径: {filename}")

        # 详细统计
        print(f"\n📈 详细统计:")
        print(f"   已取消订单: {total_cancelled} 条")
        print(f"   已发货订单: {total_shipped} 条")
        print(f"   查询时间范围: {days_back} 天")

        if total_cancelled == 0:
            print(f"\n⚠️ 提示: 在过去{days_back}天内没有找到已取消的订单")
            print(f"💡 建议: 可以尝试增加查询天数来查找更早的已取消订单")

        print(f"\n🎉 销售出库明细导出完成！")

    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    import sys

    # 默认查询过去7天的数据
    days_back = 7

    # 如果提供了命令行参数，使用指定的天数
    if len(sys.argv) > 1:
        try:
            days_back = int(sys.argv[1])
            if days_back <= 0:
                print("❌ 查询天数必须大于0")
                sys.exit(1)
        except ValueError:
            print("❌ 无效的天数参数，请输入正整数")
            sys.exit(1)

    print(f"🔍 将查询过去 {days_back} 天的已取消和已发货订单")
    print(f"💡 如需查询更长时间范围，请使用: python {sys.argv[0]} <天数>")
    print(f"   例如: python {sys.argv[0]} 30  # 查询过去30天")

    export_cancelled_shipped_extended(days_back)
