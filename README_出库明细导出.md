# 销售出库明细导出工具

## 功能说明

本工具用于导出当天已取消和已发货的销售出库明细，并根据货品名称在货品档案.xlsx中查找货品编号和货品备注。

## 文件说明

### 主要脚本

1. **export_cancelled_shipped_stockouts.py** - 完整版（需要pandas）
   - 功能最全面，支持更多Excel操作
   - 依赖：`pip install openpyxl pandas`

2. **export_cancelled_shipped_simple.py** - 简化版（仅需openpyxl）
   - 推荐使用，依赖最少
   - 依赖：`pip install openpyxl`

### 配置文件

- **config.py** - API配置信息
- **货品档案.xlsx** - 货品档案文件（需要放在同目录下）

## 安装依赖

```bash
# 简化版（推荐）
pip install openpyxl

# 完整版
pip install openpyxl pandas
```

## 使用方法

### 1. 准备货品档案文件

将货品档案Excel文件命名为 `货品档案.xlsx` 并放在脚本同目录下。

货品档案文件应包含以下字段（支持多种字段名称）：

- **货品名称字段**：货品名称、商品名称、产品名称、goods_name、product_name
- **货品编号字段**：货品编号、商品编号、产品编号、goods_no、product_no、goods_code
- **货品备注字段**：货品备注、商品备注、产品备注、goods_remark、product_remark、remark、备注

### 2. 运行脚本

```bash
# 运行简化版（推荐）
python export_cancelled_shipped_simple.py

# 或运行完整版
python export_cancelled_shipped_stockouts.py
```

### 3. 查看结果

脚本会在当前目录生成Excel文件：`销售出库明细_YYYYMMDD.xlsx`

## 输出文件结构

### 销售出库明细表

包含以下字段：

**基础信息**
- 出库单号、原始订单号、交易单号、原始交易号
- 货主编号、仓库编号、仓库名称、店铺名称、店铺编号
- 物流编码、物流名称、物流单号、快递单号
- 买家昵称、收件人信息（姓名、手机、电话、地址）
- 订单总金额、货品数量、货品种类数、重量、体积
- 备注、客服备注、买家留言、标记名称
- 发货时间、创建时间、修改时间、状态、状态名称
- 交易时间、付款时间、平台名称

**商品明细信息**
- 商品序号、规格编号、货品名称
- **货品编号**（从货品档案中匹配）
- **货品备注**（从货品档案中匹配）
- 商品数量、商品重量、商品体积、商品备注、行号

### 数据汇总表

包含以下统计信息：
- 查询日期和导出时间
- 已取消订单数、已发货订单数
- 总出库单数、总商品明细数
- 档案匹配数、匹配率
- 货品档案总数

## 查询条件

- **时间范围**：当天00:00:00 到当前时间
- **状态筛选**：
  - 状态5：已取消
  - 状态95：已发货
- **数据来源**：stockout.query API

## 货品档案匹配逻辑

1. **精确匹配**：货品名称完全相同
2. **模糊匹配**：货品名称包含关系（忽略大小写）
3. **未匹配**：货品编号和备注字段为空

## 注意事项

### 1. 货品档案文件格式

- 文件名必须为：`货品档案.xlsx`
- 第一行为表头
- 支持多种字段名称（中英文）
- 建议包含：货品名称、货品编号、货品备注

### 2. API配置

确保 `config.py` 中的API配置正确：

```python
class WDTConfig:
    SID = "您的卖家标识"
    APP_KEY = "您的应用公钥"
    APP_SECRET = "您的应用私钥"
    API_URL = "https://openapi.wdtwms.com/open_api/service.php"
```

### 3. 性能考虑

- 脚本会自动分页查询，最多查询100页
- 每页最多100条记录
- 查询时间跨度限制为1天

### 4. 错误处理

- 如果货品档案文件不存在，会提示错误但继续执行
- 如果API调用失败，会显示详细错误信息
- 如果某页数据查询失败，会跳过该页继续查询

## 示例输出

```
🚛 导出当天已取消和已发货的销售出库明细（简化版）
============================================================
📋 正在加载货品档案: 货品档案.xlsx
📋 字段列表: ['货品名称', '货品编号', '货品备注', '其他字段']
✅ 找到货品名称字段: 货品名称 (列 1)
✅ 找到货品编号字段: 货品编号 (列 2)
✅ 找到货品备注字段: 货品备注 (列 3)
✅ 成功加载 1250 条货品映射
📋 映射示例:
  1. COLORFIRE 桌面音响 CSP-5201-黑色 -> 编号: CF001, 备注: 高品质桌面音响，支持蓝牙连接...
  2. 蒸汽波鼠标垫 -> 编号: MP002, 备注: 时尚蒸汽波设计鼠标垫...
  3. COLORFIRE 桌面音响 FS-T2201-BT白色 -> 编号: CF003, 备注: 白色蓝牙音响，音质优秀...

📅 查询日期: 2025-07-11
⏰ 时间范围: 00:00:00 ~ 14:30:25
🔍 开始查询出库单...

📄 正在获取第 1 页数据...
📊 API返回: total=45, content数量=45
✅ 第 1 页: 总数据 45 条, 已取消/已发货 32 条
📈 累计已取消/已发货订单: 32 条
🏁 已获取完所有数据 (总数:45, 已查询:100)

📊 查询完成！总计获取 32 条已取消/已发货订单

📊 开始导出到Excel...
📁 文件名: 销售出库明细_20250711.xlsx
✅ 导出成功！
📊 总计导出: 32 条出库单
📦 商品明细: 85 条
🎯 档案匹配: 78 条 (91.8%)
📁 文件路径: 销售出库明细_20250711.xlsx

🎉 销售出库明细导出完成！
```

## 常见问题

### Q1: 货品档案匹配率低怎么办？

**A1**: 检查以下几点：
- 货品档案中的货品名称是否与出库单中的货品名称一致
- 是否存在多余的空格或特殊字符
- 可以手动调整货品档案中的名称以提高匹配率

### Q2: 脚本运行很慢怎么办？

**A2**: 
- 检查网络连接是否稳定
- 如果数据量很大，可以考虑分时段查询
- 确保API调用频率不超过限制

### Q3: 导出的Excel文件打不开？

**A3**:
- 确保安装了正确版本的openpyxl：`pip install --upgrade openpyxl`
- 检查磁盘空间是否充足
- 尝试用不同的Excel软件打开

### Q4: 找不到某些字段？

**A4**:
- 检查API返回的数据结构是否发生变化
- 可以在脚本中添加调试信息查看实际返回的字段
- 联系API提供方确认字段名称

## 技术支持

如有问题，请检查：
1. Python版本（建议3.7+）
2. 依赖库版本
3. API配置是否正确
4. 网络连接是否正常
5. 货品档案文件格式是否正确
