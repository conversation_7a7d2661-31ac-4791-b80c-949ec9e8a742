#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出销售出库明细到旺店通出货数据文件
将货主名称、货品编号、货品数量、备注写入到指定Excel文件的ABCDE列
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, archive_file: str = "货品档案.xlsx"):
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            from openpyxl import load_workbook
            
            print(f"📋 正在加载货品档案: {self.archive_file}")
            
            wb = load_workbook(self.archive_file, read_only=True)
            ws = wb.active
            
            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(str(cell.value).strip())
                else:
                    headers.append('')
            
            # 查找关键字段的索引
            goods_name_idx = None
            goods_no_idx = None
            goods_remark_idx = None
            
            for i, header in enumerate(headers):
                header_lower = header.lower()
                if '货品名称' in header or 'goods_name' in header_lower:
                    goods_name_idx = i
                elif '货品编号' in header or 'goods_no' in header_lower:
                    goods_no_idx = i
                elif '备注' in header or 'remark' in header_lower:
                    goods_remark_idx = i
            
            if goods_name_idx is None:
                goods_name_idx = 0
            
            # 读取数据行
            for row in ws.iter_rows(min_row=2, values_only=True):
                if not row or not any(row):
                    continue
                
                goods_name = ''
                if goods_name_idx < len(row) and row[goods_name_idx]:
                    goods_name = str(row[goods_name_idx]).strip()
                
                if not goods_name:
                    continue
                
                goods_no = ''
                if goods_no_idx is not None and goods_no_idx < len(row) and row[goods_no_idx]:
                    goods_no = str(row[goods_no_idx]).strip()
                
                goods_remark = ''
                if goods_remark_idx is not None and goods_remark_idx < len(row) and row[goods_remark_idx]:
                    goods_remark = str(row[goods_remark_idx]).strip()
                
                self.goods_map[goods_name] = {
                    'goods_no': goods_no,
                    'goods_remark': goods_remark
                }
            
            wb.close()
            print(f"✅ 成功加载 {len(self.goods_map)} 条货品映射")
            
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
    
    def get_goods_info(self, goods_name: str) -> Dict[str, str]:
        """根据货品名称获取货品信息"""
        if not goods_name:
            return {'goods_no': '', 'goods_remark': ''}
        
        # 精确匹配
        if goods_name in self.goods_map:
            return self.goods_map[goods_name]
        
        # 模糊匹配
        goods_name_lower = goods_name.lower().strip()
        for name, info in self.goods_map.items():
            if goods_name_lower in name.lower() or name.lower() in goods_name_lower:
                return info
        
        return {'goods_no': '', 'goods_remark': ''}

def export_to_wdt_data():
    """导出销售出库明细到旺店通出货数据文件"""
    
    print("🚛 导出销售出库明细到旺店通出货数据文件")
    print("=" * 60)
    
    # 初始化货品档案映射器
    goods_mapper = GoodsArchiveMapper()
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    
    # 查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 30
    }
    
    print(f"🔍 开始查询出库单...")
    
    all_stockouts = []
    page_no = 0
    
    # 分页查询所有数据
    while True:
        try:
            current_params = {**base_params, "page_no": page_no}
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                break
            
            content = response.get('content', [])
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
            
            if not page_data:
                break

            # 筛选已取消和已发货的数据
            filtered_data = []
            for item in page_data:
                if isinstance(item, dict):
                    status = str(item.get('status', ''))
                    if status in ['5', '95']:
                        filtered_data.append(item)

            all_stockouts.extend(filtered_data)
            print(f"   📊 第{page_no + 1}页: {len(filtered_data)} 条符合条件")

            page_no += 1
            
            if len(page_data) < base_params["page_size"]:
                break
            
            if page_no >= 50:
                break

        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            break
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条订单")
    
    if not all_stockouts:
        print("📝 今天没有符合条件的订单")
        return
    
    # 写入指定路径
    target_path = "C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx"
    
    print(f"\n📊 开始写入数据...")
    print(f"📁 目标文件: {target_path}")
    
    # 确保目标目录存在
    target_dir = os.path.dirname(target_path)
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"📁 创建目录: {target_dir}")
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        # 创建新工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "旺店通出库数据"
        
        # 写入表头到ABCDE列
        headers = ['货主', '货品编号', '物流单号', '货品数量', '分类']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入数据
        row = 2
        written_count = 0
        
        for stockout in all_stockouts:
            # 获取基础信息
            owner_name = stockout.get('owner_name', '')  # 货主名称
            logistics_no = stockout.get('logistics_no', '')  # 物流单号

            # 商品明细
            details_list = stockout.get('goods_detail', [])
            if details_list:
                for detail in details_list:
                    # 获取货品名称
                    goods_name = detail.get('goods_name', '')

                    # 从货品档案中查找货品编号和备注
                    goods_info = goods_mapper.get_goods_info(goods_name)

                    # 获取货品数量
                    goods_quantity = detail.get('num', '')

                    # 获取备注
                    goods_remark = detail.get('remark', '') or goods_info['goods_remark']

                    # 写入ABCDE列
                    ws.cell(row=row, column=1, value=owner_name)  # A列：货主
                    ws.cell(row=row, column=2, value=goods_info['goods_no'])  # B列：货品编号
                    ws.cell(row=row, column=3, value=logistics_no)  # C列：物流单号
                    ws.cell(row=row, column=4, value=goods_quantity)  # D列：货品数量
                    ws.cell(row=row, column=5, value=goods_remark)  # E列：分类

                    row += 1
                    written_count += 1
        
        # 自动调整列宽
        for column in ['A', 'B', 'C', 'D', 'E']:
            max_length = 0
            for cell in ws[column]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column].width = adjusted_width
        
        # 保存文件
        wb.save(target_path)
        
        print(f"✅ 写入成功！")
        print(f"📊 总计写入: {written_count} 条记录")
        print(f"📁 文件路径: {target_path}")
        print(f"📋 列结构:")
        print(f"   A列: 货主")
        print(f"   B列: 货品编号")
        print(f"   C列: 物流单号")
        print(f"   D列: 货品数量")
        print(f"   E列: 分类")
        
        print(f"\n🎉 数据导出完成！")
        
    except Exception as e:
        print(f"❌ 写入失败: {e}")
        print(f"💡 提示: 请确保目标文件没有被其他程序打开")

if __name__ == "__main__":
    export_to_wdt_data()
