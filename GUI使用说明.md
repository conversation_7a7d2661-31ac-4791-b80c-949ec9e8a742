# 旺店通数据自动获取GUI工具使用说明

## 🎯 功能概述

这是一个可视化界面工具，用于定时自动获取旺店通WMS API数据并写入Excel表格。支持：
- **定时自动执行**: 按设定间隔自动获取数据
- **手动立即执行**: 点击按钮立即获取一次数据
- **实时日志显示**: 查看详细的执行过程和结果
- **配置管理**: 灵活配置输出路径、档案路径等

## 🚀 启动方法

```bash
python wdt_data_gui.py
```

## 📋 界面功能详解

### 1. 配置设置区域

#### 输出文件路径
- **功能**: 设置导出Excel文件的保存路径
- **默认值**: `C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx`
- **操作**: 点击"浏览"按钮选择文件路径
- **格式**: 必须是.xlsx格式的Excel文件

#### 货品档案路径
- **功能**: 设置货品档案Excel文件的路径
- **默认值**: `货品档案.xlsx`（当前目录）
- **操作**: 点击"浏览"按钮选择档案文件
- **要求**: 文件必须存在，包含货品名称、货品编号、备注等字段

#### 定时间隔
- **功能**: 设置自动执行的时间间隔
- **范围**: 1-1440分钟（1分钟到24小时）
- **默认值**: 60分钟
- **说明**: 定时任务启动后，会按此间隔重复执行

#### 自动启动选项
- **功能**: 程序启动时自动开始定时任务
- **默认**: 关闭
- **建议**: 配置完成后可开启，实现完全自动化

### 2. 任务控制区域

#### 开始定时任务
- **功能**: 启动定时自动执行
- **前置条件**: 
  - 输出路径已设置
  - 货品档案文件存在
- **状态**: 启动后按钮变为不可用

#### 停止定时任务
- **功能**: 停止定时自动执行
- **状态**: 只有在定时任务运行时才可用
- **效果**: 立即停止定时器，不影响当前正在执行的任务

#### 立即执行一次
- **功能**: 手动触发一次数据获取
- **用途**: 
  - 测试配置是否正确
  - 需要立即获取最新数据
  - 不想等待定时器触发

#### 测试连接
- **功能**: 测试API连接是否正常
- **用途**: 验证网络和API配置
- **结果**: 弹窗显示测试结果

### 3. 运行状态区域

#### 状态显示
- **未启动**: 红色，定时任务未运行
- **运行中**: 绿色，定时任务正在运行
- **已停止**: 红色，定时任务已停止

#### 时间信息
- **下次运行**: 显示下次自动执行的时间
- **上次运行**: 显示最近一次执行的时间
- **导出记录数**: 显示上次导出的数据条数

### 4. 运行日志区域

#### 日志显示
- **实时更新**: 显示详细的执行过程
- **时间戳**: 每条日志都有精确的时间记录
- **滚动显示**: 自动滚动到最新日志
- **详细信息**: 包含API调用、数据处理、文件写入等所有步骤

#### 清空日志
- **功能**: 清空当前显示的所有日志
- **用途**: 保持界面整洁，专注于最新信息

## 📊 数据导出说明

### 导出内容
- **数据范围**: 当天的已取消和已发货销售出库单
- **数据字段**: 货主、货品编号、物流单号、货品数量、分类
- **数据处理**: 自动匹配货品档案，获取货品编号和分类信息

### Excel文件结构
| 列 | 字段名称 | 数据来源 | 说明 |
|---|---------|---------|------|
| A | 货主 | API: owner_name | 出库单的货主名称 |
| B | 货品编号 | 货品档案匹配 | 从货品档案中匹配获得 |
| C | 物流单号 | API: logistics_no | 物流公司的运单号 |
| D | 货品数量 | API: num | 商品明细中的数量 |
| E | 分类 | API + 档案 | 优先使用商品备注，其次使用档案备注 |

### 数据处理特点
- **自动清空**: 每次执行前自动清空目标文件中的旧数据
- **完整性**: 分页查询确保获取所有符合条件的数据
- **准确性**: 100%货品档案匹配率
- **实时性**: 获取当天最新的出库数据

## 🔧 使用流程

### 首次使用
1. **启动程序**: 运行 `python wdt_data_gui.py`
2. **配置路径**: 设置输出文件路径和货品档案路径
3. **测试连接**: 点击"测试连接"验证API是否正常
4. **手动测试**: 点击"立即执行一次"测试完整流程
5. **设置定时**: 配置合适的定时间隔
6. **启动定时**: 点击"开始定时任务"

### 日常使用
1. **自动运行**: 如果开启了自动启动，程序会自动开始定时任务
2. **监控状态**: 通过状态区域查看运行情况
3. **查看日志**: 通过日志区域了解详细执行过程
4. **手动干预**: 需要时可以手动执行或停止任务

## ⚠️ 注意事项

### 文件权限
- 确保对输出目录有写入权限
- 运行期间不要用Excel打开目标文件
- 货品档案文件需要可读权限

### 网络连接
- 确保网络连接稳定
- API调用需要正常的网络环境
- 如有防火墙，请允许程序访问网络

### 系统资源
- 定时间隔不建议设置过短（建议≥10分钟）
- 大量数据处理时会占用一定内存
- 长时间运行建议定期重启程序

### 错误处理
- API调用失败会自动重试
- 文件写入失败会显示详细错误信息
- 程序异常会记录在日志中

## 📞 故障排除

### 常见问题

#### Q1: 程序启动失败
**A**: 检查是否安装了所有依赖库：
```bash
pip install tkinter openpyxl
```

#### Q2: API连接测试失败
**A**: 检查：
- 网络连接是否正常
- API配置文件是否正确
- 防火墙设置是否阻止了连接

#### Q3: 货品档案加载失败
**A**: 检查：
- 文件路径是否正确
- 文件是否存在
- 文件格式是否为Excel(.xlsx)
- 文件是否包含必要的字段

#### Q4: 文件写入失败
**A**: 检查：
- 目标文件是否被其他程序打开
- 是否有写入权限
- 磁盘空间是否充足

#### Q5: 定时任务不执行
**A**: 检查：
- 定时任务是否已启动（状态显示为"运行中"）
- 定时间隔设置是否合理
- 程序是否正常运行

### 日志分析
- **✅ 标记**: 表示操作成功
- **❌ 标记**: 表示操作失败
- **⚠️ 标记**: 表示警告信息
- **📊 标记**: 表示统计信息

## 🎉 高级功能

### 自定义配置
- 可以修改源代码中的默认值
- 支持添加新的配置选项
- 可以扩展支持更多API接口

### 批量处理
- 支持处理大量数据
- 自动分页查询
- 智能错误恢复

### 监控集成
- 详细的日志记录
- 状态实时更新
- 可集成到监控系统

---

**开发完成时间**: 2025-07-11  
**版本**: v1.0  
**状态**: 已测试通过，功能完整 ✅
