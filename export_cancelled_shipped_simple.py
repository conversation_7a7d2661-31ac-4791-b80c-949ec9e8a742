#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出当天已取消和已发货的销售出库明细（简化版，仅使用openpyxl）
根据货品名称在货品档案.xlsx中查找货品编号和货品备注
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient

class SimpleGoodsArchiveMapper:
    """简化版货品档案映射器（仅使用openpyxl）"""
    
    def __init__(self, archive_file: str = "货品档案.xlsx"):
        """
        初始化货品档案映射器
        
        Args:
            archive_file: 货品档案Excel文件路径
        """
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            from openpyxl import load_workbook
            
            print(f"📋 正在加载货品档案: {self.archive_file}")
            
            # 读取Excel文件
            wb = load_workbook(self.archive_file, read_only=True)
            ws = wb.active
            
            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(str(cell.value).strip())
                else:
                    headers.append('')
            
            print(f"📋 字段列表: {headers}")
            
            # 查找关键字段的索引
            goods_name_idx = None
            goods_no_idx = None
            goods_remark_idx = None
            
            # 尝试不同的字段名称组合
            goods_name_fields = ['货品名称', '商品名称', '产品名称', 'goods_name', 'product_name']
            goods_no_fields = ['货品编号', '商品编号', '产品编号', 'goods_no', 'product_no', 'goods_code']
            goods_remark_fields = ['货品备注', '商品备注', '产品备注', 'goods_remark', 'product_remark', 'remark', '备注']
            
            for i, header in enumerate(headers):
                header_lower = header.lower()
                if goods_name_idx is None:
                    for field in goods_name_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_name_idx = i
                            print(f"✅ 找到货品名称字段: {header} (列 {i+1})")
                            break
                
                if goods_no_idx is None:
                    for field in goods_no_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_no_idx = i
                            print(f"✅ 找到货品编号字段: {header} (列 {i+1})")
                            break
                
                if goods_remark_idx is None:
                    for field in goods_remark_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_remark_idx = i
                            print(f"✅ 找到货品备注字段: {header} (列 {i+1})")
                            break
            
            if goods_name_idx is None:
                print("⚠️ 未找到货品名称字段，将使用第一列作为货品名称")
                goods_name_idx = 0
            
            # 读取数据行
            row_count = 0
            for row in ws.iter_rows(min_row=2, values_only=True):
                if not row or not any(row):
                    continue
                
                # 获取货品名称
                goods_name = ''
                if goods_name_idx < len(row) and row[goods_name_idx]:
                    goods_name = str(row[goods_name_idx]).strip()
                
                if not goods_name:
                    continue
                
                # 获取货品编号
                goods_no = ''
                if goods_no_idx is not None and goods_no_idx < len(row) and row[goods_no_idx]:
                    goods_no = str(row[goods_no_idx]).strip()
                
                # 获取货品备注
                goods_remark = ''
                if goods_remark_idx is not None and goods_remark_idx < len(row) and row[goods_remark_idx]:
                    goods_remark = str(row[goods_remark_idx]).strip()
                
                self.goods_map[goods_name] = {
                    'goods_no': goods_no,
                    'goods_remark': goods_remark
                }
                row_count += 1
            
            wb.close()
            
            print(f"✅ 成功加载 {len(self.goods_map)} 条货品映射")
            
            # 显示前几条映射示例
            if self.goods_map:
                print(f"📋 映射示例:")
                for i, (name, info) in enumerate(list(self.goods_map.items())[:3]):
                    print(f"  {i+1}. {name} -> 编号: {info['goods_no']}, 备注: {info['goods_remark'][:50]}...")
            
        except FileNotFoundError:
            print(f"❌ 货品档案文件未找到: {self.archive_file}")
            print("请确保文件存在于当前目录")
        except ImportError:
            print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
    
    def get_goods_info(self, goods_name: str) -> Dict[str, str]:
        """
        根据货品名称获取货品信息
        
        Args:
            goods_name: 货品名称
            
        Returns:
            包含货品编号和备注的字典
        """
        if not goods_name:
            return {'goods_no': '', 'goods_remark': ''}
        
        # 精确匹配
        if goods_name in self.goods_map:
            return self.goods_map[goods_name]
        
        # 模糊匹配
        goods_name_lower = goods_name.lower().strip()
        for name, info in self.goods_map.items():
            if goods_name_lower in name.lower() or name.lower() in goods_name_lower:
                return info
        
        return {'goods_no': '', 'goods_remark': ''}

def export_cancelled_shipped_simple():
    """导出当天已取消和已发货的销售出库明细（简化版）"""
    
    print("🚛 导出当天已取消和已发货的销售出库明细（简化版）")
    print("=" * 60)
    
    # 初始化货品档案映射器
    goods_mapper = SimpleGoodsArchiveMapper()
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 查询参数 - 减少页面大小以确保API正常返回
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 50  # 减少页面大小
    }
    
    print(f"🔍 开始查询出库单...")
    
    all_stockouts = []
    page_no = 0
    total_records = 0

    # 首先获取总记录数
    try:
        first_response = client.call_api('stockout.query', base_params)
        total_records = int(first_response.get('total', 0))
        print(f"📊 API返回总记录数: {total_records}")
    except Exception as e:
        print(f"❌ 获取总记录数失败: {e}")

    while True:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")

        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }

            # 调用API
            response = client.call_api('stockout.query', current_params)

            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                break

            # 获取数据
            content = response.get('content', [])
            total = response.get('total', 0)

            print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")

            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []

            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据")
                break

            # 筛选已取消(status=5)和已发货(status=95)的数据
            filtered_data = []
            status_count = {}
            cancelled_count = 0
            shipped_count = 0

            for item in page_data:
                if isinstance(item, dict):
                    status = str(item.get('status', ''))
                    # 统计所有状态
                    status_count[status] = status_count.get(status, 0) + 1

                    if status in ['5', '95']:  # 已取消或已发货状态
                        filtered_data.append(item)
                        if status == '5':
                            cancelled_count += 1
                        elif status == '95':
                            shipped_count += 1

            print(f"✅ 第 {page_no + 1} 页: 总数据 {len(page_data)} 条, 已取消 {cancelled_count} 条, 已发货 {shipped_count} 条")

            # 显示状态分布（仅在第一页显示）
            if page_no == 0:
                print(f"📊 状态分布: {status_count}")

            if filtered_data:
                all_stockouts.extend(filtered_data)
                print(f"📈 累计已取消/已发货订单: {len(all_stockouts)} 条")

            # 检查是否还有更多数据
            total_fetched_all_pages = (page_no + 1) * base_params["page_size"]
            try:
                total_int = int(total) if total else 0

                # 强制查询更多页，直到真正没有数据
                # 只有当页面返回数据为0时才停止
                if len(page_data) == 0:
                    print(f"🏁 已获取完所有数据 (当前页无数据，总数:{total}, 已查询页数:{page_no + 1})")
                    break
                # 如果当前页数据少于页面大小，可能是最后一页，但继续查询一页确认
                elif len(page_data) < base_params["page_size"]:
                    print(f"📊 当前页数据不足页面大小，可能接近结束 (当前页:{len(page_data)}, 页面大小:{base_params['page_size']})")
                    # 继续查询下一页确认
                else:
                    print(f"📊 当前页数据充足，继续查询下一页 (当前页:{len(page_data)}, 页面大小:{base_params['page_size']})")

            except (ValueError, TypeError):
                # 如果total不是数字，则根据页面数据量判断
                if len(page_data) == 0:
                    print(f"🏁 已获取完所有数据 (当前页无数据，已查询页数:{page_no + 1})")
                    break

            page_no += 1

            # 安全限制：最多查询200页（增加限制以获取更多数据）
            if page_no >= 200:
                print(f"⚠️ 已查询200页，停止查询")
                break

        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            break
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已取消/已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已取消或已发货的订单")
        return
    
    # 导出到Excel
    import time
    timestamp = int(time.time())
    filename = f"销售出库明细_{today.strftime('%Y%m%d')}_{timestamp}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建主表
        ws_main = wb.active
        ws_main.title = "销售出库明细"
        
        # 定义表头
        headers = [
            '出库单号', '原始订单号', '交易单号', '原始交易号',
            '货主编号', '货主名称', '仓库编号', '仓库名称', '店铺名称', '店铺编号',
            '物流编码', '物流名称', '物流单号', '快递单号',
            '买家昵称', '收件人姓名', '收件人手机', '收件人电话',
            '收件人省份', '收件人城市', '收件人区县', '收件人地址', '收件人区域',
            '订单总金额', '货品数量', '货品种类数', '重量', '体积', '计算重量',
            '备注', '客服备注', '买家留言', '标记名称', '打印备注',
            '发货时间', '创建时间', '修改时间', '状态', '状态名称',
            '交易时间', '付款时间', '平台名称', '交易确认时间', '交易创建时间',
            '拣货单号', '拣货类型', '包装评分', '拣货评分', '检验评分',
            '操作员', '检验员', '打印员', '拣货员', '检验员名称', '包装员',
            # 商品明细字段
            '商品序号', '规格编号', '规格名称', '货品名称', '货品编号', '货品备注',
            '商品数量', '商品毛重', '商品净重', '商品体积', '商品备注', '条形码',
            '单位比例', '订单价格', '商品总金额', '商品图片'
        ]
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入数据
        row = 2
        matched_count = 0
        total_goods_count = 0
        
        for stockout in all_stockouts:
            # 获取状态名称
            status = str(stockout.get('status', ''))
            status_name = '已取消' if status == '5' else '已发货' if status == '95' else f'状态{status}'
            
            # 基础出库单信息
            base_data = [
                stockout.get('stockout_no', ''),
                stockout.get('src_order_no', ''),
                stockout.get('trade_no', ''),
                stockout.get('src_tids', ''),
                stockout.get('owner_no', ''),
                stockout.get('owner_name', ''),  # 添加货主名称
                stockout.get('warehouse_no', ''),
                stockout.get('warehouse_name', ''),
                stockout.get('shop_name', ''),
                stockout.get('shop_no', ''),
                stockout.get('logistics_code', ''),
                stockout.get('logistics_name', ''),
                stockout.get('logistics_no', ''),
                stockout.get('express_no', ''),
                stockout.get('buyer_nick', ''),
                stockout.get('receiver_name', ''),
                stockout.get('receiver_mobile', ''),
                stockout.get('receiver_telno', ''),
                stockout.get('receiver_province', ''),
                stockout.get('receiver_city', ''),
                stockout.get('receiver_district', ''),
                stockout.get('receiver_address', ''),
                stockout.get('receiver_area', ''),  # 添加收件人区域
                stockout.get('total_amount', ''),
                stockout.get('goods_count', ''),
                stockout.get('goods_type_count', ''),
                stockout.get('weight', ''),
                stockout.get('volume', ''),
                stockout.get('calc_weight', ''),  # 添加计算重量
                stockout.get('remark', ''),
                stockout.get('cs_remark', ''),
                stockout.get('buyer_message', ''),
                stockout.get('flag_name', ''),
                stockout.get('print_remark', ''),  # 添加打印备注
                stockout.get('consign_time', ''),
                stockout.get('created', ''),
                stockout.get('modified', ''),
                stockout.get('status', ''),
                status_name,
                stockout.get('trade_time', ''),
                stockout.get('pay_time', ''),
                stockout.get('platform_name', ''),
                stockout.get('trade_check_time', ''),  # 添加交易确认时间
                stockout.get('trade_create_time', ''),  # 添加交易创建时间
                stockout.get('picklist_no', ''),  # 添加拣货单号
                stockout.get('pick_type', ''),  # 添加拣货类型
                stockout.get('pack_score', ''),  # 添加包装评分
                stockout.get('pick_score', ''),  # 添加拣货评分
                stockout.get('examine_score', ''),  # 添加检验评分
                stockout.get('operator_name', ''),  # 添加操作员
                stockout.get('checker_name', ''),  # 添加检验员
                stockout.get('printer_name', ''),  # 添加打印员
                stockout.get('picker_name', ''),  # 添加拣货员
                stockout.get('examiner_name', ''),  # 添加检验员名称
                stockout.get('packer_name', '')  # 添加包装员
            ]
            
            # 商品明细
            details_list = stockout.get('goods_detail', [])
            if details_list:
                for i, detail in enumerate(details_list):
                    total_goods_count += 1
                    
                    # 获取货品名称
                    goods_name = detail.get('goods_name', '')

                    # 从货品档案中查找货品编号和备注
                    goods_info = goods_mapper.get_goods_info(goods_name)

                    # 统计匹配情况
                    if goods_info['goods_no'] or goods_info['goods_remark']:
                        matched_count += 1

                    goods_data = [
                        i + 1,  # 商品序号
                        detail.get('spec_no', ''),
                        detail.get('spec_name', ''),  # 添加规格名称
                        goods_name,
                        goods_info['goods_no'],  # 从档案中获取的货品编号
                        goods_info['goods_remark'],  # 从档案中获取的货品备注
                        detail.get('num', ''),
                        detail.get('gross_weight', ''),  # 商品毛重
                        detail.get('net_weight', ''),  # 商品净重
                        detail.get('volume', ''),
                        detail.get('remark', ''),
                        detail.get('barcode', ''),  # 添加条形码
                        detail.get('unit_ratio', ''),  # 添加单位比例
                        detail.get('order_price', ''),  # 添加订单价格
                        detail.get('goods_total_amount', ''),  # 添加商品总金额
                        detail.get('img_url', '')  # 添加商品图片
                    ]
                    
                    # 写入完整行数据
                    full_row_data = base_data + goods_data
                    for col, value in enumerate(full_row_data, 1):
                        ws_main.cell(row=row, column=col, value=str(value))
                    row += 1
            else:
                # 没有商品明细的出库单，商品相关字段留空
                goods_data = ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']  # 更新为16个字段
                full_row_data = base_data + goods_data
                for col, value in enumerate(full_row_data, 1):
                    ws_main.cell(row=row, column=col, value=str(value))
                row += 1
        
        # 自动调整列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 创建汇总表
        ws_summary = wb.create_sheet("数据汇总")
        
        # 汇总表头
        summary_headers = ['统计项目', '数值', '说明']
        for col, header in enumerate(summary_headers, 1):
            cell = ws_summary.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 统计数据
        cancelled_count = len([s for s in all_stockouts if str(s.get('status', '')) == '5'])
        shipped_count = len([s for s in all_stockouts if str(s.get('status', '')) == '95'])
        match_rate = (matched_count / total_goods_count * 100) if total_goods_count > 0 else 0
        
        # 写入汇总数据
        summary_data = [
            ('查询日期', today.strftime('%Y-%m-%d'), '数据查询的日期'),
            ('导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '文件生成时间'),
            ('已取消订单数', cancelled_count, '状态为5的出库单数量'),
            ('已发货订单数', shipped_count, '状态为95的出库单数量'),
            ('总出库单数', len(all_stockouts), '已取消和已发货的出库单总数'),
            ('总商品明细数', total_goods_count, '所有商品明细条目数量'),
            ('档案匹配数', matched_count, '成功匹配到货品档案的商品数量'),
            ('匹配率', f'{match_rate:.1f}%', '货品档案匹配成功率'),
            ('货品档案总数', len(goods_mapper.goods_map), '货品档案中的总记录数')
        ]
        
        for row_idx, (item, value, desc) in enumerate(summary_data, 2):
            ws_summary.cell(row=row_idx, column=1, value=item)
            ws_summary.cell(row=row_idx, column=2, value=str(value))
            ws_summary.cell(row=row_idx, column=3, value=desc)
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_stockouts)} 条出库单")
        print(f"📦 商品明细: {total_goods_count} 条")
        print(f"🎯 档案匹配: {matched_count} 条 ({match_rate:.1f}%)")
        print(f"📁 文件路径: {filename}")
        
        print(f"\n🎉 销售出库明细导出完成！")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_cancelled_shipped_simple()
