#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置功能测试脚本
用于验证配置保存和加载功能
"""

import json
import os
from datetime import datetime

def test_config_operations():
    """测试配置操作"""
    
    config_file = "wdt_gui_config.json"
    
    print("🔧 配置功能测试")
    print("=" * 50)
    
    # 测试1: 创建默认配置
    print("\n1. 测试创建默认配置")
    default_config = {
        "output_path": "C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx",
        "archive_path": "货品档案.xlsx",
        "interval_minutes": 60,
        "auto_start": True,
        "timer_mode": "daily",
        "daily_hour": 9,
        "daily_minute": 0,
        "daily_times": ["09:00", "14:00", "18:00"],
        "last_saved": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        print(f"✅ 默认配置创建成功: {config_file}")
    except Exception as e:
        print(f"❌ 默认配置创建失败: {e}")
        return
    
    # 测试2: 读取配置
    print("\n2. 测试读取配置")
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        print("✅ 配置读取成功")
        print("📋 配置内容:")
        for key, value in loaded_config.items():
            print(f"   {key}: {value}")
            
    except Exception as e:
        print(f"❌ 配置读取失败: {e}")
        return
    
    # 测试3: 修改配置
    print("\n3. 测试修改配置")
    try:
        # 修改一些配置项
        loaded_config["interval_minutes"] = 30
        loaded_config["daily_times"].append("21:00")
        loaded_config["auto_start"] = False
        loaded_config["last_saved"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 保存修改
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(loaded_config, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置修改成功")
        print("📝 修改内容:")
        print(f"   间隔时间: 60 → 30 分钟")
        print(f"   每日时间: 添加 21:00")
        print(f"   自动启动: True → False")
        
    except Exception as e:
        print(f"❌ 配置修改失败: {e}")
        return
    
    # 测试4: 验证修改
    print("\n4. 测试验证修改")
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            verify_config = json.load(f)
        
        # 验证修改是否生效
        assert verify_config["interval_minutes"] == 30
        assert "21:00" in verify_config["daily_times"]
        assert verify_config["auto_start"] == False
        
        print("✅ 配置修改验证成功")
        print("📋 当前配置:")
        for key, value in verify_config.items():
            print(f"   {key}: {value}")
            
    except Exception as e:
        print(f"❌ 配置修改验证失败: {e}")
        return
    
    # 测试5: 配置备份和恢复
    print("\n5. 测试配置备份和恢复")
    try:
        # 创建备份
        backup_file = f"{config_file}.backup"
        with open(config_file, 'r', encoding='utf-8') as src:
            with open(backup_file, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        
        print(f"✅ 配置备份成功: {backup_file}")
        
        # 模拟配置损坏
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write("invalid json content")
        
        print("⚠️ 模拟配置文件损坏")
        
        # 尝试读取损坏的配置
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                json.load(f)
            print("❌ 应该检测到配置损坏")
        except json.JSONDecodeError:
            print("✅ 正确检测到配置损坏")
        
        # 从备份恢复
        with open(backup_file, 'r', encoding='utf-8') as src:
            with open(config_file, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        
        print("✅ 从备份恢复配置成功")
        
        # 清理备份文件
        os.remove(backup_file)
        
    except Exception as e:
        print(f"❌ 备份恢复测试失败: {e}")
    
    # 测试6: 中文路径支持
    print("\n6. 测试中文路径支持")
    try:
        chinese_config = {
            "output_path": "C:/用户/测试/旺店通出货数据/数据文件.xlsx",
            "archive_path": "货品档案_中文名称.xlsx",
            "description": "这是一个包含中文的配置文件测试"
        }
        
        chinese_file = "test_chinese_config.json"
        with open(chinese_file, 'w', encoding='utf-8') as f:
            json.dump(chinese_config, f, ensure_ascii=False, indent=2)
        
        # 读取验证
        with open(chinese_file, 'r', encoding='utf-8') as f:
            loaded_chinese = json.load(f)
        
        assert loaded_chinese["output_path"] == chinese_config["output_path"]
        assert loaded_chinese["archive_path"] == chinese_config["archive_path"]
        
        print("✅ 中文路径支持测试成功")
        print(f"   输出路径: {loaded_chinese['output_path']}")
        print(f"   档案路径: {loaded_chinese['archive_path']}")
        
        # 清理测试文件
        os.remove(chinese_file)
        
    except Exception as e:
        print(f"❌ 中文路径支持测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 配置功能测试完成")
    
    # 显示最终配置文件内容
    if os.path.exists(config_file):
        print(f"\n📄 最终配置文件内容 ({config_file}):")
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            print(content)
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")

def test_auto_start_logic():
    """测试自动启动逻辑"""
    
    print("\n🚀 自动启动逻辑测试")
    print("=" * 50)
    
    # 测试场景
    test_cases = [
        {
            "name": "完整配置 + 自动启动启用",
            "config": {
                "output_path": "C:/test/output.xlsx",
                "archive_path": "货品档案.xlsx",
                "auto_start": True,
                "timer_mode": "daily",
                "daily_times": ["09:00", "14:00"]
            },
            "file_exists": True,
            "expected": "应该自动启动"
        },
        {
            "name": "缺少输出路径",
            "config": {
                "output_path": "",
                "archive_path": "货品档案.xlsx",
                "auto_start": True,
                "timer_mode": "daily",
                "daily_times": ["09:00"]
            },
            "file_exists": True,
            "expected": "不应该启动 - 缺少输出路径"
        },
        {
            "name": "档案文件不存在",
            "config": {
                "output_path": "C:/test/output.xlsx",
                "archive_path": "不存在的文件.xlsx",
                "auto_start": True,
                "timer_mode": "daily",
                "daily_times": ["09:00"]
            },
            "file_exists": False,
            "expected": "不应该启动 - 档案文件不存在"
        },
        {
            "name": "自动启动未启用",
            "config": {
                "output_path": "C:/test/output.xlsx",
                "archive_path": "货品档案.xlsx",
                "auto_start": False,
                "timer_mode": "daily",
                "daily_times": ["09:00"]
            },
            "file_exists": True,
            "expected": "不应该启动 - 自动启动未启用"
        },
        {
            "name": "每日模式无执行时间",
            "config": {
                "output_path": "C:/test/output.xlsx",
                "archive_path": "货品档案.xlsx",
                "auto_start": True,
                "timer_mode": "daily",
                "daily_times": []
            },
            "file_exists": True,
            "expected": "不应该启动 - 无执行时间"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}")
        print(f"   配置: {case['config']}")
        print(f"   文件存在: {case['file_exists']}")
        print(f"   预期结果: {case['expected']}")
        
        # 模拟验证逻辑
        should_start = True
        reason = ""
        
        if not case['config'].get('auto_start', False):
            should_start = False
            reason = "自动启动未启用"
        elif not case['config'].get('output_path', ''):
            should_start = False
            reason = "未设置输出文件路径"
        elif not case['file_exists']:
            should_start = False
            reason = "货品档案文件不存在"
        elif case['config'].get('timer_mode') == 'daily' and not case['config'].get('daily_times', []):
            should_start = False
            reason = "每日定时模式需要至少设置一个执行时间"
        
        if should_start:
            print(f"   ✅ 验证通过 - 可以自动启动")
        else:
            print(f"   ❌ 验证失败 - {reason}")

def main():
    """主函数"""
    print("🧪 旺店通GUI配置功能测试套件")
    print("=" * 60)
    
    # 运行配置操作测试
    test_config_operations()
    
    # 运行自动启动逻辑测试
    test_auto_start_logic()
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成")

if __name__ == "__main__":
    main()
