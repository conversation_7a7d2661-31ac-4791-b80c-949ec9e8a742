#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
尝试其他可能的API接口获取货品备注
探索可能包含备注信息的其他接口
"""

import json
from datetime import datetime
from wdt_post_client import WDTPostClient

class AlternativeRemarksSearcher:
    """替代备注搜索器"""
    
    def __init__(self):
        self.client = WDTPostClient()
        self.owner_no = "AJT-XLSWDT"
        
    def try_goods_related_apis(self):
        """尝试货品相关的其他API"""
        
        print("🔍 尝试货品相关的其他API接口...")
        
        # 可能包含备注信息的API列表
        potential_apis = [
            # 货品基础信息相关
            'goods.info.query',
            'goods.detail.query', 
            'goods.master.query',
            'goods.base.query',
            
            # 商品信息相关
            'product.info.query',
            'product.detail.query',
            'product.master.query',
            
            # 规格信息相关
            'spec.info.query',
            'spec.detail.query',
            'spec.master.query',
            
            # 可能的其他命名方式
            'item.info.query',
            'item.detail.query',
            'sku.info.query',
            'sku.detail.query',
            
            # 档案相关
            'archive.goods.query',
            'master.goods.query',
            'base.goods.query',
            
            # 可能的描述相关接口
            'goods.description.query',
            'goods.remark.query',
            'goods.note.query'
        ]
        
        successful_apis = []
        
        for api_name in potential_apis:
            print(f"\n📋 测试API: {api_name}")
            
            # 尝试不同的参数组合
            test_params = [
                # 基础查询
                {"owner_no": self.owner_no, "page_size": 5, "page_no": 0},
                
                # 带spec_no的查询
                {"owner_no": self.owner_no, "spec_no": "2255825473338900736"},
                
                # 带goods_no的查询
                {"owner_no": self.owner_no, "goods_no": "A10204900115-GB"},
                
                # 不带owner_no的查询
                {"page_size": 5, "page_no": 0},
                {"spec_no": "2255825473338900736"}
            ]
            
            for i, params in enumerate(test_params, 1):
                print(f"  🔍 尝试参数组合 {i}: {params}")
                
                try:
                    response = self.client.call_api(api_name, params)
                    
                    if response and 'content' in response:
                        content = response.get('content', [])
                        total = response.get('total', 0)
                        
                        print(f"    ✅ API调用成功: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
                        
                        if isinstance(content, list) and content:
                            first_item = content[0]
                            
                            # 查找备注相关字段
                            remark_fields = []
                            for key, value in first_item.items():
                                if any(keyword in key.lower() for keyword in [
                                    'remark', 'note', 'memo', 'desc', 'comment', 
                                    'detail', 'info', 'explain', 'instruction'
                                ]):
                                    if value and str(value).strip():
                                        remark_fields.append((key, value))
                                        print(f"      🔍 找到备注字段 {key}: {value}")
                            
                            if remark_fields:
                                successful_apis.append({
                                    'api_name': api_name,
                                    'params': params,
                                    'remark_fields': remark_fields,
                                    'sample_data': first_item
                                })
                                print(f"    ✅ 找到有用的备注信息！")
                                break
                        
                        elif total == 0:
                            print(f"    📝 接口可用但无数据")
                        
                        else:
                            print(f"    ❌ 返回数据格式异常")
                    
                    else:
                        print(f"    ❌ API返回数据为空或格式错误")
                
                except Exception as e:
                    error_msg = str(e)
                    if "无该接口" in error_msg or "调用权限" in error_msg:
                        print(f"    ❌ 无权限: {error_msg}")
                        break  # 如果没有权限，不用尝试其他参数
                    elif "未部署" in error_msg or "暂不支持" in error_msg:
                        print(f"    ❌ 不支持: {error_msg}")
                        break
                    else:
                        print(f"    ❌ 调用失败: {e}")
        
        return successful_apis
    
    def try_order_related_apis(self):
        """尝试订单相关的API，看是否有商品备注"""
        
        print(f"\n🔍 尝试订单相关的API，查找商品备注...")
        
        # 订单相关API
        order_apis = [
            'trade.query',
            'order.query', 
            'sales.order.query',
            'purchase.order.query'
        ]
        
        today = datetime.now()
        start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
        
        if end_time > datetime.now():
            end_time = datetime.now()
        
        successful_apis = []
        
        for api_name in order_apis:
            print(f"\n📋 测试订单API: {api_name}")
            
            try:
                params = {
                    "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "page_size": 5,
                    "page_no": 0
                }
                
                response = self.client.call_api(api_name, params)
                
                if response and 'content' in response:
                    content = response.get('content', [])
                    
                    if isinstance(content, list) and content:
                        first_order = content[0]
                        
                        # 查找商品明细中的备注
                        goods_detail = first_order.get('goods_detail', [])
                        if isinstance(goods_detail, list) and goods_detail:
                            for detail in goods_detail:
                                for key, value in detail.items():
                                    if any(keyword in key.lower() for keyword in [
                                        'remark', 'note', 'memo', 'desc', 'comment'
                                    ]):
                                        if value and str(value).strip():
                                            print(f"  ✅ 在订单中找到商品备注 {key}: {value}")
                                            successful_apis.append({
                                                'api_name': api_name,
                                                'field': key,
                                                'value': value,
                                                'source': 'order_goods_detail'
                                            })
            
            except Exception as e:
                print(f"  ❌ {api_name} 调用失败: {e}")
        
        return successful_apis
    
    def save_results(self, goods_apis, order_apis):
        """保存搜索结果"""
        
        results = {
            'search_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'goods_apis_results': goods_apis,
            'order_apis_results': order_apis,
            'summary': {
                'successful_goods_apis': len(goods_apis),
                'successful_order_apis': len(order_apis),
                'total_found_remark_sources': len(goods_apis) + len(order_apis)
            }
        }
        
        filename = f"alternative_remark_search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 搜索结果已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("🔍 尝试其他API接口获取货品备注")
    print("=" * 60)
    
    searcher = AlternativeRemarksSearcher()
    
    # 尝试货品相关API
    goods_results = searcher.try_goods_related_apis()
    
    # 尝试订单相关API
    order_results = searcher.try_order_related_apis()
    
    # 保存结果
    results_file = searcher.save_results(goods_results, order_results)
    
    # 总结
    print(f"\n📊 搜索结果总结:")
    print(f"✅ 找到有备注的货品API: {len(goods_results)} 个")
    print(f"✅ 找到有备注的订单API: {len(order_results)} 个")
    
    if len(goods_results) > 0:
        print(f"\n🎉 在货品API中找到备注信息:")
        for result in goods_results:
            print(f"  📋 {result['api_name']}: {len(result['remark_fields'])} 个备注字段")
    
    if len(order_results) > 0:
        print(f"\n🎉 在订单API中找到备注信息:")
        for result in order_results:
            print(f"  📋 {result['api_name']}: {result['field']} = {result['value']}")
    
    if len(goods_results) == 0 and len(order_results) == 0:
        print(f"\n❌ 没有在其他API中找到备注信息")
        print(f"💡 建议:")
        print(f"1. 联系旺店通技术支持确认备注信息的正确获取方式")
        print(f"2. 检查WMS系统界面，确认是否真的设置了商品备注")
        print(f"3. 询问是否有专门的商品描述或备注管理模块")
    
    return searcher

if __name__ == "__main__":
    main()
