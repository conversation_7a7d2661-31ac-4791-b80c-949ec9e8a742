#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出当天已取消和已发货的销售出库明细
根据货品名称在货品档案.xlsx中查找货品编号和货品备注
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient
import pandas as pd

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, archive_file: str = "货品档案.xlsx"):
        """
        初始化货品档案映射器
        
        Args:
            archive_file: 货品档案Excel文件路径
        """
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            print(f"📋 正在加载货品档案: {self.archive_file}")
            
            # 读取Excel文件
            df = pd.read_excel(self.archive_file)
            
            print(f"📊 货品档案包含 {len(df)} 条记录")
            print(f"📋 字段列表: {list(df.columns)}")
            
            # 创建货品名称到货品信息的映射
            for index, row in df.iterrows():
                # 尝试不同的字段名称组合
                goods_name_fields = ['货品名称', '商品名称', '产品名称', 'goods_name', 'product_name']
                goods_no_fields = ['货品编号', '商品编号', '产品编号', 'goods_no', 'product_no', 'goods_code']
                goods_remark_fields = ['货品备注', '商品备注', '产品备注', 'goods_remark', 'product_remark', 'remark', '备注']
                
                goods_name = None
                goods_no = None
                goods_remark = None
                
                # 查找货品名称
                for field in goods_name_fields:
                    if field in df.columns and pd.notna(row[field]):
                        goods_name = str(row[field]).strip()
                        break
                
                # 查找货品编号
                for field in goods_no_fields:
                    if field in df.columns and pd.notna(row[field]):
                        goods_no = str(row[field]).strip()
                        break
                
                # 查找货品备注
                for field in goods_remark_fields:
                    if field in df.columns and pd.notna(row[field]):
                        goods_remark = str(row[field]).strip()
                        break
                
                if goods_name:
                    self.goods_map[goods_name] = {
                        'goods_no': goods_no or '',
                        'goods_remark': goods_remark or ''
                    }
            
            print(f"✅ 成功加载 {len(self.goods_map)} 条货品映射")
            
            # 显示前几条映射示例
            if self.goods_map:
                print(f"📋 映射示例:")
                for i, (name, info) in enumerate(list(self.goods_map.items())[:3]):
                    print(f"  {i+1}. {name} -> 编号: {info['goods_no']}, 备注: {info['goods_remark'][:50]}...")
            
        except FileNotFoundError:
            print(f"❌ 货品档案文件未找到: {self.archive_file}")
            print("请确保文件存在于当前目录")
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
    
    def get_goods_info(self, goods_name: str) -> Dict[str, str]:
        """
        根据货品名称获取货品信息
        
        Args:
            goods_name: 货品名称
            
        Returns:
            包含货品编号和备注的字典
        """
        if not goods_name:
            return {'goods_no': '', 'goods_remark': ''}
        
        # 精确匹配
        if goods_name in self.goods_map:
            return self.goods_map[goods_name]
        
        # 模糊匹配
        goods_name_lower = goods_name.lower().strip()
        for name, info in self.goods_map.items():
            if goods_name_lower in name.lower() or name.lower() in goods_name_lower:
                return info
        
        return {'goods_no': '', 'goods_remark': ''}

def export_cancelled_shipped_stockouts():
    """导出当天已取消和已发货的销售出库明细"""
    
    print("🚛 导出当天已取消和已发货的销售出库明细")
    print("=" * 60)
    
    # 初始化货品档案映射器
    goods_mapper = GoodsArchiveMapper()
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 100
    }
    
    print(f"🔍 开始查询出库单...")
    
    all_stockouts = []
    page_no = 0
    
    while True:
        print(f"\n📄 正在获取第 {page_no + 1} 页数据...")
        
        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }
            
            # 调用API
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                print(f"❌ 第 {page_no + 1} 页响应为空")
                break
            
            # 获取数据
            content = response.get('content', [])
            total = response.get('total', 0)
            
            print(f"📊 API返回: total={total}, content数量={len(content) if isinstance(content, list) else 'N/A'}")
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', [])
                if not isinstance(page_data, list):
                    page_data = [page_data] if page_data else []
            
            if not page_data:
                print(f"📝 第 {page_no + 1} 页无数据")
                break
            
            # 筛选已取消(status=5)和已发货(status=95)的数据
            filtered_data = []
            for item in page_data:
                if isinstance(item, dict):
                    status = str(item.get('status', ''))
                    if status in ['5', '95']:  # 已取消或已发货状态
                        filtered_data.append(item)
            
            print(f"✅ 第 {page_no + 1} 页: 总数据 {len(page_data)} 条, 已取消/已发货 {len(filtered_data)} 条")
            
            if filtered_data:
                all_stockouts.extend(filtered_data)
                print(f"📈 累计已取消/已发货订单: {len(all_stockouts)} 条")
            
            # 检查是否还有更多数据
            total_fetched = (page_no + 1) * base_params["page_size"]
            if total_fetched >= total or len(page_data) < base_params["page_size"]:
                print(f"🏁 已获取完所有数据 (总数:{total}, 已查询:{total_fetched})")
                break
            
            page_no += 1
            
            # 安全限制：最多查询100页
            if page_no >= 100:
                print(f"⚠️ 已查询100页，停止查询")
                break
                
        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            break
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已取消/已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已取消或已发货的订单")
        return
    
    # 导出到Excel
    filename = f"销售出库明细_{today.strftime('%Y%m%d')}.xlsx"
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        print(f"\n📊 开始导出到Excel...")
        print(f"📁 文件名: {filename}")
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建主表
        ws_main = wb.active
        ws_main.title = "销售出库明细"
        
        # 定义表头
        headers = [
            '出库单号', '原始订单号', '交易单号', '原始交易号',
            '货主编号', '仓库编号', '仓库名称', '店铺名称', '店铺编号',
            '物流编码', '物流名称', '物流单号', '快递单号',
            '买家昵称', '收件人姓名', '收件人手机', '收件人电话',
            '收件人省份', '收件人城市', '收件人区县', '收件人地址',
            '订单总金额', '货品数量', '货品种类数', '重量', '体积',
            '备注', '客服备注', '买家留言', '标记名称',
            '发货时间', '创建时间', '修改时间', '状态', '状态名称',
            '交易时间', '付款时间', '平台名称',
            # 商品明细字段
            '商品序号', '规格编号', '货品名称', '货品编号', '货品备注', 
            '商品数量', '商品重量', '商品体积', '商品备注', '行号'
        ]
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws_main.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 写入数据
        row = 2
        for stockout in all_stockouts:
            # 获取状态名称
            status = str(stockout.get('status', ''))
            status_name = '已取消' if status == '5' else '已发货' if status == '95' else f'状态{status}'
            
            # 基础出库单信息
            base_data = [
                stockout.get('stockout_no', ''),
                stockout.get('src_order_no', ''),
                stockout.get('trade_no', ''),
                stockout.get('src_tids', ''),
                stockout.get('owner_no', ''),
                stockout.get('warehouse_no', ''),
                stockout.get('warehouse_name', ''),
                stockout.get('shop_name', ''),
                stockout.get('shop_no', ''),
                stockout.get('logistics_code', ''),
                stockout.get('logistics_name', ''),
                stockout.get('logistics_no', ''),
                stockout.get('express_no', ''),
                stockout.get('buyer_nick', ''),
                stockout.get('receiver_name', ''),
                stockout.get('receiver_mobile', ''),
                stockout.get('receiver_telno', ''),
                stockout.get('receiver_province', ''),
                stockout.get('receiver_city', ''),
                stockout.get('receiver_district', ''),
                stockout.get('receiver_address', ''),
                stockout.get('total_amount', ''),
                stockout.get('goods_count', ''),
                stockout.get('goods_type_count', ''),
                stockout.get('weight', ''),
                stockout.get('volume', ''),
                stockout.get('remark', ''),
                stockout.get('cs_remark', ''),
                stockout.get('buyer_message', ''),
                stockout.get('flag_name', ''),
                stockout.get('consign_time', ''),
                stockout.get('created', ''),
                stockout.get('modified', ''),
                stockout.get('status', ''),
                status_name,
                stockout.get('trade_time', ''),
                stockout.get('pay_time', ''),
                stockout.get('platform_name', '')
            ]
            
            # 商品明细
            details_list = stockout.get('details_list', [])
            if details_list:
                for i, detail in enumerate(details_list):
                    # 获取货品名称
                    goods_name = detail.get('goods_name', '')
                    
                    # 从货品档案中查找货品编号和备注
                    goods_info = goods_mapper.get_goods_info(goods_name)
                    
                    goods_data = [
                        i + 1,  # 商品序号
                        detail.get('spec_no', ''),
                        goods_name,
                        goods_info['goods_no'],  # 从档案中获取的货品编号
                        goods_info['goods_remark'],  # 从档案中获取的货品备注
                        detail.get('num', ''),
                        detail.get('weight', ''),
                        detail.get('volume', ''),
                        detail.get('remark', ''),
                        detail.get('orderline_no', '')
                    ]
                    
                    # 写入完整行数据
                    full_row_data = base_data + goods_data
                    for col, value in enumerate(full_row_data, 1):
                        ws_main.cell(row=row, column=col, value=str(value))
                    row += 1
            else:
                # 没有商品明细的出库单，商品相关字段留空
                goods_data = ['', '', '', '', '', '', '', '', '', '']
                full_row_data = base_data + goods_data
                for col, value in enumerate(full_row_data, 1):
                    ws_main.cell(row=row, column=col, value=str(value))
                row += 1
        
        # 自动调整列宽
        for column in ws_main.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws_main.column_dimensions[column_letter].width = adjusted_width
        
        # 保存文件
        wb.save(filename)
        
        print(f"✅ 导出成功！")
        print(f"📊 总计导出: {len(all_stockouts)} 条出库单")
        print(f"📁 文件路径: {filename}")
        
        print(f"\n🎉 销售出库明细导出完成！")
        
    except ImportError:
        print("❌ 缺少必要的库，请先安装: pip install openpyxl pandas")
    except Exception as e:
        print(f"❌ 导出失败: {e}")

if __name__ == "__main__":
    export_cancelled_shipped_stockouts()
