import os
import time
import pandas as pd
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from datetime import datetime
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter

# 尝试导入可选依赖
try:
    from wxauto import WeChat
    WECHAT_AVAILABLE = True
except ImportError:
    WECHAT_AVAILABLE = False
    print("警告: wxauto 未安装，微信发送功能将不可用")

try:
    import sv_ttk  # 导入Sun Valley主题包
    THEME_AVAILABLE = True
except ImportError:
    THEME_AVAILABLE = False
    print("警告: sv_ttk 未安装，主题功能将不可用")

try:
    from PIL import Image, ImageDraw, ImageFont
    import win32com.client
    EXCEL_TO_IMAGE_AVAILABLE = True
except ImportError:
    EXCEL_TO_IMAGE_AVAILABLE = False
    print("警告: PIL 或 win32com 未安装，Excel转图片功能将不可用")

# 检查微信发送功能是否可用（只检查库，不测试连接）
try:
    from wxauto import WeChat
    import pythoncom
    WECHAT_AVAILABLE = True
    print("✅ wxauto库已加载，微信发送功能已准备就绪")
except ImportError:
    WECHAT_AVAILABLE = False
    import sys
    if not getattr(sys, 'frozen', False):  # 只在非exe环境显示安装命令
        print("❌ wxauto库未安装，微信发送功能不可用")
        print("📋 安装命令: pip install wxauto")
    else:
        print("❌ exe环境中缺少wxauto依赖，微信发送功能不可用")

def check_wechat_login_status():
    """实时检查微信是否已登录（只在需要时调用）"""
    if not WECHAT_AVAILABLE:
        return False, "wxauto库不可用"

    try:
        from wxauto import WeChat
        import pythoncom

        # 初始化COM组件
        try:
            pythoncom.CoInitialize()
            com_initialized = True
        except:
            com_initialized = False

        try:
            # 尝试连接微信
            wx = WeChat()

            # 检查微信是否已登录（尝试获取会话列表）
            try:
                sessions = wx.GetSessionList()
                if sessions:
                    return True, "微信已登录，发送功能可用"
                else:
                    return False, "微信未登录或无会话"
            except Exception as e:
                error_msg = str(e)
                if "微信未登录" in error_msg or "登录" in error_msg:
                    return False, "微信未登录"
                elif "UIAutomationCore.dll" in error_msg:
                    return False, "缺少UIAutomationCore.dll，请安装Windows Update KB971513"
                else:
                    return False, f"微信状态检测失败: {error_msg}"

        except Exception as e:
            error_msg = str(e)
            if "微信未登录" in error_msg or "登录" in error_msg:
                return False, "微信未登录"
            elif "UIAutomationCore.dll" in error_msg:
                return False, "缺少UIAutomationCore.dll，请安装Windows Update KB971513"
            elif "微信" in error_msg:
                return False, "微信客户端未运行或未安装"
            else:
                return False, f"微信检测失败: {error_msg}"
        finally:
            if com_initialized:
                try:
                    pythoncom.CoUninitialize()
                except:
                    pass

    except Exception as e:
        return False, f"检测过程异常: {str(e)}"

def wait_for_wechat_login(max_wait_time=300, check_interval=10):
    """等待微信登录（用于重试机制）"""
    import time

    print(f"⏳ 等待微信登录，最多等待{max_wait_time}秒...")
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        is_logged_in, status_msg = check_wechat_login_status()
        if is_logged_in:
            print(f"✅ 微信已登录！{status_msg}")
            return True

        remaining_time = max_wait_time - (time.time() - start_time)
        print(f"⏳ 微信未登录，{remaining_time:.0f}秒后超时，请登录微信...")
        time.sleep(check_interval)

    print("❌ 等待超时，微信仍未登录")
    return False

def check_wechat_status():
    """检查微信状态"""
    try:
        import psutil
        import subprocess

        # 检查微信进程是否运行
        wechat_running = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'WeChat.exe' in proc.info['name']:
                    wechat_running = True
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return wechat_running
    except ImportError:
        print("警告: psutil 未安装，无法检测微信状态")
        return True  # 假设微信已运行

def start_wechat():
    """启动微信"""
    try:
        import subprocess
        import os
        import winreg

        # 尝试从注册表获取微信安装路径
        wechat_path = None
        try:
            # 查找微信安装路径
            key_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\WeChat",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\WeChat",
                r"SOFTWARE\Tencent\WeChat"
            ]

            for key_path in key_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as key:
                        install_location = winreg.QueryValueEx(key, "InstallLocation")[0]
                        wechat_exe = os.path.join(install_location, "WeChat.exe")
                        if os.path.exists(wechat_exe):
                            wechat_path = wechat_exe
                            break
                except (FileNotFoundError, OSError):
                    continue
        except Exception:
            pass

        # 如果注册表找不到，尝试常见路径
        if not wechat_path:
            common_paths = [
                r"C:\Program Files\Tencent\WeChat\WeChat.exe",
                r"C:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
                os.path.expanduser(r"~\AppData\Local\WeChat\WeChat.exe"),
                os.path.expanduser(r"~\AppData\Roaming\Tencent\WeChat\WeChat.exe")
            ]

            for path in common_paths:
                if os.path.exists(path):
                    wechat_path = path
                    break

        if wechat_path:
            print(f"启动微信: {wechat_path}")
            subprocess.Popen([wechat_path], shell=True)
            return True
        else:
            print("未找到微信安装路径")
            return False

    except Exception as e:
        print(f"启动微信失败: {str(e)}")
        return False

def wait_for_wechat_login(timeout=60):
    """等待微信登录完成"""
    try:
        from wxauto import WeChat
        import time

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                wx = WeChat()
                # 尝试获取当前聊天，如果成功说明已登录
                wx.GetAllMessage()
                print("微信登录成功")
                return True
            except Exception:
                print("等待微信登录...")
                time.sleep(3)

        print("等待微信登录超时")
        return False

    except Exception as e:
        print(f"检查微信登录状态失败: {str(e)}")
        return False

class WeChatSender:
    def __init__(self, contact_name):
        if not WECHAT_AVAILABLE:
            raise ImportError("wxauto 未安装，无法使用微信发送功能")

        self.contact_name = contact_name
        self.lock = threading.Lock()
        self.wx = None
        self._ensure_wechat_ready()

    def _ensure_wechat_ready(self):
        """确保微信已启动并登录"""
        try:
            # 检查微信是否运行
            if not check_wechat_status():
                print("微信未运行，正在启动微信...")
                if start_wechat():
                    print("微信启动成功，等待登录...")
                    # 等待微信启动和登录
                    time.sleep(5)  # 给微信一些启动时间
                    if not wait_for_wechat_login(60):
                        raise Exception("微信启动后登录超时，请手动登录微信")
                else:
                    raise Exception("无法启动微信，请手动启动微信")

            # 初始化微信连接
            self.wx = WeChat()
            self._verify_contact()

        except Exception as e:
            raise Exception(f"微信准备失败: {str(e)}")

    def _verify_contact(self):
        """验证联系人是否存在，支持微信重连"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.wx.ChatWith(self.contact_name)
                return
            except Exception as e:
                error_msg = str(e)
                print(f"验证联系人失败 (第{attempt + 1}次): {error_msg}")

                # 检查是否是微信连接问题
                if self._is_wechat_connection_error(error_msg):
                    print("检测到微信连接问题，尝试重新连接...")
                    if self._reconnect_wechat():
                        continue  # 重新连接成功，重试验证联系人

                if attempt == max_retries - 1:
                    raise ValueError(f"找不到微信联系人：{self.contact_name}") from e

                time.sleep(2)  # 等待后重试

    def _is_wechat_connection_error(self, error_msg):
        """判断是否是微信连接错误"""
        connection_error_keywords = [
            "微信未登录",
            "微信已退出",
            "连接失败",
            "窗口句柄",
            "SetWindowPos",
            "找不到窗口",
            "微信进程",
            "1400"
        ]
        return any(keyword in error_msg for keyword in connection_error_keywords)

    def _reconnect_wechat(self):
        """重新连接微信"""
        try:
            print("正在重新连接微信...")

            # 检查微信是否还在运行
            if not check_wechat_status():
                print("微信已退出，正在重新启动...")
                if not start_wechat():
                    print("重新启动微信失败")
                    return False

                print("等待微信启动...")
                time.sleep(5)

                if not wait_for_wechat_login(60):
                    print("等待微信登录超时")
                    return False

            # 重新创建微信连接
            from wxauto import WeChat
            self.wx = WeChat()
            print("微信重新连接成功")
            return True

        except Exception as e:
            print(f"重新连接微信失败: {str(e)}")
            return False

    def send_file(self, file_path, callback=None):
        """安全发送文件方法，支持微信中途退出自动重连"""
        with self.lock:
            try:
                if not os.path.exists(file_path):
                    message = f"文件不存在：{file_path}"
                    print(message)
                    if callback: callback(message)
                    return

                # 等待文件写入完成
                file_size = -1
                for _ in range(5):
                    new_size = os.path.getsize(file_path)
                    if new_size == file_size:
                        break
                    file_size = new_size
                    time.sleep(1)

                # 尝试发送文件，支持微信重连
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        self.wx.ChatWith(self.contact_name)
                        self.wx.SendFiles(file_path)
                        message = f"成功发送文件：{file_path}"
                        print(message)
                        if callback: callback(message)
                        return

                    except Exception as e:
                        error_msg = str(e)
                        print(f"发送文件失败 (第{attempt + 1}次): {error_msg}")

                        # 检查是否是微信连接问题
                        if self._is_wechat_connection_error(error_msg):
                            print("检测到微信连接问题，尝试重新连接...")
                            if self._reconnect_wechat():
                                print("微信重新连接成功，继续尝试发送...")
                                continue  # 重新连接成功，重试发送
                            else:
                                message = f"微信重新连接失败，文件发送中止：{error_msg}"
                                print(message)
                                if callback: callback(message)
                                return

                        if attempt == max_retries - 1:
                            message = f"文件发送失败（已重试{max_retries}次）：{error_msg}"
                            print(message)
                            if callback: callback(message)
                        else:
                            time.sleep(3)  # 等待后重试

            except Exception as e:
                message = f"文件发送过程出错：{str(e)}"
                print(message)
                if callback: callback(message)

def safe_wechat_send(contact_name, file_path, callback=None):
    """安全的微信发送函数，支持微信中途退出自动重连"""
    if not WECHAT_AVAILABLE:
        message = "微信发送功能不可用，请安装 wxauto"
        if callback: callback(message)
        return False

    import pythoncom

    # 在当前线程中初始化COM
    try:
        pythoncom.CoInitialize()
        com_initialized = True
    except:
        com_initialized = False

    def is_wechat_connection_error(error_msg):
        """判断是否是微信连接错误"""
        connection_error_keywords = [
            "微信未登录", "微信已退出", "连接失败", "窗口句柄",
            "SetWindowPos", "找不到窗口", "微信进程", "1400"
        ]
        return any(keyword in error_msg for keyword in connection_error_keywords)

    def reconnect_wechat():
        """重新连接微信"""
        try:
            print("检测到微信连接问题，正在重新连接...")

            # 检查微信是否还在运行
            if not check_wechat_status():
                print("微信已退出，正在重新启动...")
                if not start_wechat():
                    print("重新启动微信失败")
                    return False

                print("等待微信启动...")
                time.sleep(5)

                if not wait_for_wechat_login(60):
                    print("等待微信登录超时")
                    return False

            print("微信重新连接成功")
            return True

        except Exception as e:
            print(f"重新连接微信失败: {str(e)}")
            return False

    try:
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                print(f"尝试发送文件 (第{attempt + 1}次)...")

                # 创建新的微信实例
                from wxauto import WeChat
                wx = WeChat()

                # 等待微信初始化
                time.sleep(2)

                # 切换到联系人
                wx.ChatWith(contact_name)
                time.sleep(1)

                # 发送文件
                wx.SendFiles(file_path)

                message = f"成功发送文件：{file_path}"
                print(message)
                if callback: callback(message)
                return True

            except Exception as e:
                error_msg = str(e)
                print(f"第{attempt + 1}次发送失败: {error_msg}")

                # 检查是否是微信连接问题
                if is_wechat_connection_error(error_msg):
                    if reconnect_wechat():
                        print("微信重新连接成功，继续尝试发送...")
                        continue  # 重新连接成功，重试发送
                    else:
                        message = f"微信重新连接失败，文件发送中止：{error_msg}"
                        print(message)
                        if callback: callback(message)
                        return False

                if attempt < max_attempts - 1:
                    print(f"等待5秒后重试...")
                    time.sleep(5)
                else:
                    message = f"文件发送失败（已重试{max_attempts}次）：{error_msg}"
                    print(message)
                    if callback: callback(message)
                    return False

        return False

    finally:
        # 清理COM
        if com_initialized:
            try:
                pythoncom.CoUninitialize()
            except:
                pass

def excel_to_image(excel_file_path, output_image_path=None):
    """将Excel文件的已使用区域转换为图片"""
    if not EXCEL_TO_IMAGE_AVAILABLE:
        raise ImportError("PIL 或 win32com 未安装，无法使用Excel转图片功能")

    import pythoncom
    import win32com.client

    # 在当前线程中初始化COM
    try:
        pythoncom.CoInitialize()
        com_initialized = True
    except:
        # 如果已经初始化过，继续使用
        com_initialized = False

    excel_app = None
    try:
        # 如果没有指定输出路径，自动生成
        if output_image_path is None:
            base_name = os.path.splitext(excel_file_path)[0]
            output_image_path = f"{base_name}_表格截图.png"

        # 使用win32com打开Excel
        excel_app = win32com.client.Dispatch("Excel.Application")
        excel_app.Visible = False
        excel_app.DisplayAlerts = False

        # 打开工作簿
        workbook = excel_app.Workbooks.Open(os.path.abspath(excel_file_path))
        worksheet = workbook.ActiveSheet

        # 获取已使用的区域
        used_range = worksheet.UsedRange

        # 复制已使用区域为图片
        used_range.CopyPicture(1, 2)  # xlScreen, xlBitmap

        # 创建一个新的工作表来粘贴图片
        temp_sheet = workbook.Worksheets.Add()
        temp_sheet.Paste()

        # 获取粘贴的图片对象
        picture = temp_sheet.Pictures(1)

        # 导出为图片文件
        picture.Copy()

        # 关闭工作簿
        workbook.Close(False)

        # 使用PIL处理剪贴板中的图片
        from PIL import ImageGrab
        img = ImageGrab.grabclipboard()

        if img:
            # 保存图片
            img.save(output_image_path, 'PNG', quality=95, dpi=(300, 300))
            return output_image_path
        else:
            raise Exception("无法从剪贴板获取图片")

    except Exception as e:
        raise Exception(f"Excel转图片失败: {str(e)}")
    finally:
        # 清理Excel应用程序
        if excel_app:
            try:
                excel_app.Quit()
            except:
                pass

        # 清理COM（只有当前线程初始化的才清理）
        if com_initialized:
            try:
                pythoncom.CoUninitialize()
            except:
                pass

def excel_to_image_simple(excel_file_path, output_image_path=None):
    """精确匹配Excel字体的图片转换功能"""
    try:
        from openpyxl import load_workbook
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        raise ImportError("需要安装 openpyxl 和 PIL: pip install openpyxl pillow")

    try:
        # 如果没有指定输出路径，自动生成
        if output_image_path is None:
            base_name = os.path.splitext(excel_file_path)[0]
            output_image_path = f"{base_name}_表格截图.png"

        # 使用openpyxl直接读取Excel文件以保持格式
        wb = load_workbook(excel_file_path)
        ws = wb.active

        # 获取已使用的区域（openpyxl的正确方法）
        min_row = ws.min_row
        max_row = ws.max_row
        min_col = ws.min_column
        max_col = ws.max_column

        if not min_row or not max_row or not min_col or not max_col:
            raise Exception("Excel文件中没有数据")

        # 分析Excel文件中的实际字体设置
        def get_excel_font_info():
            """获取Excel文件中的字体信息"""
            font_info = {
                'title_size': 18,    # 默认标题字体大小
                'header_size': 11,   # 默认表头字体大小
                'cell_size': 11,     # 默认单元格字体大小
                'font_name': '微软雅黑'  # 默认字体名称
            }

            try:
                # 检查标题行字体（第一行）
                if min_row >= 1:
                    title_cell = ws.cell(row=1, column=min_col)
                    if title_cell.font and title_cell.font.size:
                        font_info['title_size'] = title_cell.font.size
                    if title_cell.font and title_cell.font.name:
                        font_info['font_name'] = title_cell.font.name

                # 检查表头字体（第二行或数据开始行）
                header_row = 2 if min_row == 1 else min_row
                if header_row <= max_row:
                    header_cell = ws.cell(row=header_row, column=min_col)
                    if header_cell.font and header_cell.font.size:
                        font_info['header_size'] = header_cell.font.size

                # 检查数据单元格字体
                data_row = header_row + 1 if header_row < max_row else header_row
                if data_row <= max_row:
                    data_cell = ws.cell(row=data_row, column=min_col)
                    if data_cell.font and data_cell.font.size:
                        font_info['cell_size'] = data_cell.font.size

            except Exception as e:
                print(f"获取字体信息时出错: {e}")

            return font_info

        # 获取Excel的实际字体信息
        excel_font_info = get_excel_font_info()
        print(f"检测到Excel字体信息: {excel_font_info}")

        # 根据Excel字体信息计算精确的尺寸
        # PT到像素的转换：使用高DPI转换以提高质量 (300 DPI: 1pt ≈ 4.17px)
        pt_to_px = 4.17  # 300 DPI转换，提供更高质量

        # 计算基于Excel字体的实际尺寸
        title_px = int(excel_font_info['title_size'] * pt_to_px)
        header_px = int(excel_font_info['header_size'] * pt_to_px)
        cell_px = int(excel_font_info['cell_size'] * pt_to_px)

        # 动态计算单元格尺寸 - 基于实际字体大小，增加质量系数
        quality_factor = 1.2  # 质量提升系数
        base_cell_width = max(150, int(cell_px * 3.5 * quality_factor))  # 增大宽度计算
        base_cell_height = max(40, int(cell_px * 1.8 * quality_factor))  # 增大高度计算
        title_height = max(50, int(title_px * 1.5 * quality_factor))     # 增大标题高度
        margin = max(40, int(cell_px * 1.5 * quality_factor))           # 增大边距

        # 边框优化参数 - 确保边框清晰可见
        border_quality_factor = 2.0  # 边框质量提升系数
        thin_border = max(2, int(cell_px * 0.05 * border_quality_factor))    # 细边框
        medium_border = max(3, int(cell_px * 0.08 * border_quality_factor))  # 中等边框
        thick_border = max(4, int(cell_px * 0.12 * border_quality_factor))   # 粗边框

        print(f"边框宽度设置: 细{thin_border}px, 中{medium_border}px, 粗{thick_border}px")

        def draw_clear_border(draw, rect, border_width, color='#000000'):
            """绘制清晰的边框"""
            x1, y1, x2, y2 = rect

            # 绘制多层边框以增强清晰度
            for i in range(border_width):
                # 上边框
                draw.line([(x1 + i, y1 + i), (x2 - i, y1 + i)], fill=color, width=1)
                # 下边框
                draw.line([(x1 + i, y2 - i), (x2 - i, y2 - i)], fill=color, width=1)
                # 左边框
                draw.line([(x1 + i, y1 + i), (x1 + i, y2 - i)], fill=color, width=1)
                # 右边框
                draw.line([(x2 - i, y1 + i), (x2 - i, y2 - i)], fill=color, width=1)

        def draw_enhanced_border(draw, rect, border_width, color='#000000', style='solid'):
            """绘制增强的边框，支持不同样式"""
            x1, y1, x2, y2 = rect

            if style == 'solid':
                # 实线边框 - 使用多次绘制确保清晰
                for i in range(border_width):
                    offset = i
                    # 绘制四条边，每条边都单独绘制以确保清晰
                    draw.rectangle([x1 + offset, y1 + offset, x2 - offset, y2 - offset],
                                 outline=color, width=1)
            elif style == 'double':
                # 双线边框
                outer_width = max(1, border_width // 2)
                inner_width = border_width - outer_width

                # 外边框
                for i in range(outer_width):
                    draw.rectangle([x1 + i, y1 + i, x2 - i, y2 - i], outline=color, width=1)

                # 内边框（留一点间隙）
                gap = 2
                for i in range(inner_width):
                    draw.rectangle([x1 + outer_width + gap + i, y1 + outer_width + gap + i,
                                  x2 - outer_width - gap - i, y2 - outer_width - gap - i],
                                 outline=color, width=1)

        # 根据内容和字体大小调整列宽 - 高质量计算
        col_widths = []
        for col in range(min_col, max_col + 1):
            max_width = base_cell_width
            for row in range(min_row, max_row + 1):
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    text = str(cell.value)
                    text_length = len(text)

                    # 精确计算中文和英文字符宽度
                    chinese_chars = sum(1 for char in text if ord(char) > 127)
                    english_chars = text_length - chinese_chars

                    # 中文字符宽度 = 字体大小 * 0.9，英文字符宽度 = 字体大小 * 0.55
                    chinese_width = chinese_chars * cell_px * 0.9
                    english_width = english_chars * cell_px * 0.55

                    # 计算总宽度，添加内边距
                    content_width = chinese_width + english_width
                    padding = cell_px * 1.5  # 内边距
                    estimated_width = max(content_width + padding, base_cell_width)

                    # 限制最大宽度，但允许更大的范围以提高质量
                    max_width = max(max_width, min(estimated_width, cell_px * 15))
            col_widths.append(int(max_width))

        # 计算图片总尺寸
        total_width = sum(col_widths) + margin * 2
        total_height = (max_row - min_row + 1) * base_cell_height + title_height + margin * 2

        # 创建图片
        img = Image.new('RGB', (int(total_width), int(total_height)), 'white')
        draw = ImageDraw.Draw(img)

        # 加载字体 - 使用Excel文件中的实际字体信息
        font_name = excel_font_info['font_name']

        def load_font_with_fallback(font_name, size):
            """加载字体，包含备用方案"""
            font_paths = [
                f"{font_name}.ttc",  # 微软雅黑等
                f"{font_name}.ttf",  # 标准TTF字体
                "msyh.ttc",          # 微软雅黑备用
                "arial.ttf",         # Arial备用
                "simhei.ttf",        # 黑体备用
            ]

            for font_path in font_paths:
                try:
                    return ImageFont.truetype(font_path, size)
                except:
                    continue

            # 如果所有字体都失败，使用默认字体
            return ImageFont.load_default()

        # 加载与Excel完全匹配的字体
        title_font = load_font_with_fallback(font_name, title_px)
        header_font = load_font_with_fallback(font_name, header_px)
        cell_font = load_font_with_fallback(font_name, cell_px)

        print(f"使用字体: {font_name}, 大小: 标题{title_px}px, 表头{header_px}px, 单元格{cell_px}px")

        # 绘制标题（如果第一行是合并的标题）
        title_drawn = False
        if min_row == 1:
            first_cell = ws.cell(row=1, column=min_col)
            if first_cell.value and isinstance(first_cell.value, str):
                # 检查是否是标题行（通常包含"汇总"等关键词）
                if any(keyword in str(first_cell.value) for keyword in ['汇总', '统计', '报表', '数据']):
                    # 绘制标题背景和清晰边框
                    title_rect = [margin, margin, total_width - margin, margin + title_height]
                    draw.rectangle(title_rect, fill='#DCE6F1')
                    draw_clear_border(draw, title_rect, thick_border, '#000000')

                    # 绘制标题文本
                    title_text = str(first_cell.value)
                    bbox = draw.textbbox((0, 0), title_text, font=title_font)
                    text_width = bbox[2] - bbox[0]
                    text_x = (total_width - text_width) // 2
                    text_y = margin + (title_height - (bbox[3] - bbox[1])) // 2
                    draw.text((text_x, text_y), title_text, fill='#000080', font=title_font)

                    title_drawn = True
                    min_row += 1  # 跳过标题行

        # 计算起始Y位置
        start_y = margin + (title_height if title_drawn else 0)

        # 绘制表格
        current_y = start_y
        for row_idx, row in enumerate(range(min_row, max_row + 1)):
            current_x = margin

            # 判断是否是表头行
            is_header = (row == min_row)
            # 判断是否是总计行
            is_total = any(str(ws.cell(row=row, column=col).value or '').strip() == '总计'
                          for col in range(min_col, max_col + 1))

            for col_idx, col in enumerate(range(min_col, max_col + 1)):
                cell = ws.cell(row=row, column=col)
                cell_width = col_widths[col_idx]

                # 确定单元格样式
                if is_header:
                    bg_color = '#4472C4'
                    text_color = 'white'
                    font = header_font
                    border_width = medium_border
                    border_style = 'solid'
                elif is_total:
                    bg_color = '#DCE6F1'
                    text_color = '#000080'
                    font = cell_font
                    border_width = medium_border
                    border_style = 'solid'
                else:
                    bg_color = '#F8F9FA' if row_idx % 2 == 0 else 'white'
                    text_color = 'black'
                    font = cell_font
                    border_width = thin_border
                    border_style = 'solid'

                # 绘制单元格背景
                cell_rect = [current_x, current_y, current_x + cell_width, current_y + base_cell_height]
                draw.rectangle(cell_rect, fill=bg_color)

                # 绘制清晰的边框
                draw_enhanced_border(draw, cell_rect, border_width, '#000000', border_style)

                # 绘制单元格文本
                if cell.value is not None:
                    text = str(cell.value)
                    # 处理数字格式
                    if isinstance(cell.value, (int, float)) and not is_header:
                        if cell.value >= 1000:
                            text = f"{cell.value:,}"  # 添加千分位分隔符

                    # 计算文本位置（居中）
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                    text_x = current_x + (cell_width - text_width) // 2
                    text_y = current_y + (base_cell_height - text_height) // 2

                    draw.text((text_x, text_y), text, fill=text_color, font=font)

                current_x += cell_width

            current_y += base_cell_height

        # 保存图片
        img.save(output_image_path, 'PNG', quality=95, dpi=(300, 300))
        return output_image_path

    except Exception as e:
        raise Exception(f"Excel转图片失败: {str(e)}")

class WangdiantongApp(tk.Tk):
    def __init__(self):
        super().__init__()

        # 设置窗口属性
        self.title("旺店通出货数据处理与微信发送工具")
        self.geometry("900x700")
        self.configure(bg="#f0f0f0")

        # 配置文件路径
        self.config_file = "wechat_monitor_config.txt"

        # 应用Sun Valley主题
        self.theme_var = tk.StringVar(value="light")
        if THEME_AVAILABLE:
            try:
                sv_ttk.set_theme("light")
            except:
                pass

        # 初始化变量
        self.file_path = tk.StringVar(value=self._load_last_file_path())
        self.monitoring = False
        self.monitor_thread = None
        self.last_update_time = tk.StringVar(value="未开始监控")
        self.status_text = tk.StringVar(value="就绪")
        self.log_messages = []

        # 微信发送相关变量
        self.contact_var = tk.StringVar(value=self._load_last_contact())
        self.wechat_sender = None
        self.auto_send_enabled = tk.BooleanVar(value=True)
        self.send_as_image = tk.BooleanVar(value=True)  # 默认发送图片

        # 状态颜色配置
        self.status_colors = {
            "info": "#000000",     # 黑色
            "success": "#008800",  # 绿色
            "error": "#CC0000",    # 红色
            "warning": "#FF8800"   # 橙色
        }

        # 创建界面元素
        self._create_widgets()

        # 设置关闭窗口事件
        self.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _load_last_contact(self):
        """从配置文件加载上次使用的联系人名称"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    content = f.read().strip()
                    return content if content else "FCK"  # 如果文件为空，返回默认值
            return "FCK"  # 默认值
        except Exception as e:
            print(f"加载联系人配置失败: {e}")
            return "FCK"  # 出错时返回默认值

    def _save_contact(self):
        """保存当前联系人名称到配置文件"""
        try:
            contact = self.contact_var.get().strip()
            if contact:  # 只有当联系人不为空时才保存
                with open(self.config_file, "w", encoding="utf-8") as f:
                    f.write(contact)
        except Exception as e:
            print(f"保存联系人配置失败: {e}")

    def _create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建样式
        style = ttk.Style()
        style.configure("TButton", font=("微软雅黑", 10))
        style.configure("TLabel", font=("微软雅黑", 10))
        style.configure("Header.TLabel", font=("微软雅黑", 16, "bold"))
        style.configure("Status.TLabel", font=("微软雅黑", 10, "bold"))

        # 顶部工具栏
        self.toolbar_frame = ttk.Frame(main_frame)
        self.toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # 功能状态显示
        self.feature_frame = ttk.LabelFrame(self.toolbar_frame, text="功能状态")
        wechat_status = "可用" if WECHAT_AVAILABLE else "不可用"
        wechat_color = "#008800" if WECHAT_AVAILABLE else "#CC0000"
        self.wechat_status_label = ttk.Label(self.feature_frame, text=f"微信发送: {wechat_status}",
                                           foreground=wechat_color, font=("Arial", 9))
        self.wechat_status_label.pack(padx=10, pady=5)
        self.feature_frame.pack(side=tk.LEFT, padx=10, pady=5, fill=tk.Y)

        # 状态指示器
        self.status_indicator_frame = ttk.LabelFrame(self.toolbar_frame, text="监控状态")
        self.status_indicator = ttk.Label(self.status_indicator_frame, text="未启动",
                                        foreground="#CC0000", font=("Arial", 10, "bold"))
        self.status_indicator.pack(padx=15, pady=5)
        self.status_indicator_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill=tk.Y)

        # 标题
        header_label = ttk.Label(main_frame, text="旺店通出货数据处理与微信发送工具", style="Header.TLabel")
        header_label.pack(pady=(0, 20))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件设置")
        file_frame.pack(fill=tk.X, pady=10)

        ttk.Label(file_frame, text="监控文件路径:").grid(row=0, column=0, padx=5, pady=10, sticky=tk.W)
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).grid(row=0, column=1, padx=5, pady=10, sticky=tk.W+tk.E)
        ttk.Button(file_frame, text="浏览...", command=self._browse_file).grid(row=0, column=2, padx=5, pady=10)

        # 配置列权重
        file_frame.grid_columnconfigure(1, weight=1)

        # 微信设置区域
        wechat_frame = ttk.LabelFrame(main_frame, text="微信发送设置")
        wechat_frame.pack(fill=tk.X, pady=10)

        # 第一行：联系人和自动发送
        ttk.Label(wechat_frame, text="微信联系人:").grid(row=0, column=0, padx=5, pady=10, sticky=tk.W)
        self.contact_entry = ttk.Entry(wechat_frame, textvariable=self.contact_var, width=30)
        self.contact_entry.grid(row=0, column=1, padx=5, pady=10, sticky=tk.W)

        # 添加多人模式说明
        help_label = ttk.Label(wechat_frame, text="(多人发送请用分号;分隔，如：张三;李四;王五)",
                              foreground="gray", font=("微软雅黑", 8))
        help_label.grid(row=1, column=1, padx=5, pady=(0, 5), sticky=tk.W)

        self.auto_send_checkbox = ttk.Checkbutton(wechat_frame, text="处理完成后自动发送",
                                                variable=self.auto_send_enabled)
        self.auto_send_checkbox.grid(row=0, column=2, padx=20, pady=10, sticky=tk.W)

        # 第二行：发送格式选择
        ttk.Label(wechat_frame, text="发送格式:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)

        format_frame = ttk.Frame(wechat_frame)
        format_frame.grid(row=2, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W)

        self.image_radio = ttk.Radiobutton(format_frame, text="发送表格图片 (推荐)",
                                         variable=self.send_as_image, value=True)
        self.image_radio.pack(side=tk.LEFT, padx=10)

        self.file_radio = ttk.Radiobutton(format_frame, text="发送Excel文件",
                                        variable=self.send_as_image, value=False)
        self.file_radio.pack(side=tk.LEFT, padx=10)

        # 功能状态提示
        image_status = "可用" if EXCEL_TO_IMAGE_AVAILABLE else "不可用"
        image_color = "#008800" if EXCEL_TO_IMAGE_AVAILABLE else "#CC0000"
        self.image_status_label = ttk.Label(format_frame, text=f"(图片功能: {image_status})",
                                          foreground=image_color, font=("Arial", 8))
        self.image_status_label.pack(side=tk.LEFT, padx=10)

        # 如果微信功能不可用，禁用相关控件
        if not WECHAT_AVAILABLE:
            self.contact_entry.config(state="disabled")
            self.auto_send_checkbox.config(state="disabled")
            self.image_radio.config(state="disabled")
            self.file_radio.config(state="disabled")
            self.auto_send_enabled.set(False)

        # 如果图片功能不可用，禁用图片选项并默认选择文件
        if not EXCEL_TO_IMAGE_AVAILABLE:
            self.image_radio.config(state="disabled")
            self.send_as_image.set(False)

        # 配置列权重
        wechat_frame.grid_columnconfigure(1, weight=1)

        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="监控状态")
        status_frame.pack(fill=tk.X, pady=10)

        ttk.Label(status_frame, text="当前状态:").grid(row=0, column=0, padx=5, pady=10, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.status_text, style="Status.TLabel").grid(row=0, column=1, padx=5, pady=10, sticky=tk.W)

        ttk.Label(status_frame, text="最后更新时间:").grid(row=1, column=0, padx=5, pady=10, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.last_update_time).grid(row=1, column=1, padx=5, pady=10, sticky=tk.W)
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=20)

        self.start_button = ttk.Button(control_frame, text="开始监控", command=self._start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(control_frame, text="停止监控", command=self._stop_monitoring, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="立即处理", command=self._process_now).pack(side=tk.LEFT, padx=5)

        self.test_wechat_btn = ttk.Button(control_frame, text="测试微信发送", command=self._test_wechat_send)
        self.test_wechat_btn.pack(side=tk.LEFT, padx=5)

        # 如果微信功能不可用，禁用测试按钮
        if not WECHAT_AVAILABLE:
            self.test_wechat_btn.config(state="disabled")

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="处理与发送日志")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X)
        self.clear_btn = ttk.Button(log_toolbar, text="清空记录", command=self._clear_log, width=10)
        self.clear_btn.pack(side=tk.RIGHT, padx=5, pady=2)

        # 美化的文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, font=("Consolas", 9))
        self.log_text.pack(padx=5, pady=5, fill=tk.BOTH, expand=True)
        self.log_text.tag_configure("info", foreground=self.status_colors["info"])
        self.log_text.tag_configure("success", foreground=self.status_colors["success"])
        self.log_text.tag_configure("error", foreground=self.status_colors["error"])
        self.log_text.tag_configure("warning", foreground=self.status_colors["warning"])

        # 状态栏
        status_bar = ttk.Label(self, text="版本: 2.0.0 - 集成微信发送功能", relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 添加初始日志
        self._log_message("欢迎使用旺店通出货数据处理与微信发送工具", "info")
        self._log_message("程序将自动开始监控文件变化", "info")

        # 启动后自动开始监控
        self.after(1000, self._auto_start_monitoring)  # 延迟1秒后自动开始监控

    def _auto_start_monitoring(self):
        """自动开始监控"""
        try:
            # 检查文件路径是否存在
            file_path = self.file_path.get().strip()
            if not file_path:
                self._log_message("使用默认文件路径进行监控", "info")
                return

            if not os.path.exists(file_path):
                self._log_message(f"监控文件不存在: {file_path}", "warning")
                self._log_message("程序将等待文件创建后开始监控", "info")

            # 自动开始监控
            self._start_monitoring()
            self._log_message("已自动开始文件监控", "success")

        except Exception as e:
            self._log_message(f"自动启动监控失败: {str(e)}", "error")

    def _toggle_theme(self):
        """切换主题"""
        if not THEME_AVAILABLE:
            self._log_message("主题功能不可用，请安装 sv_ttk", "warning")
            return

        try:
            theme = self.theme_var.get()
            sv_ttk.set_theme(theme)
            self._log_message(f"已切换到{('浅色' if theme == 'light' else '深色')}主题", "info")
        except Exception as e:
            self._log_message(f"主题切换失败: {str(e)}", "error")

    def _clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self._log_message("日志已清空", "info")

    def _test_wechat_send(self):
        """测试微信发送功能（支持多人模式）"""
        if not WECHAT_AVAILABLE:
            self._log_message("微信发送功能不可用，请安装 wxauto", "error")
            return

        # 首先检查微信登录状态
        self._log_message("正在检查微信登录状态...", "info")
        is_logged_in, status_msg = check_wechat_login_status()

        if not is_logged_in:
            self._log_message(f"微信状态检查失败: {status_msg}", "error")
            self._log_message("请确保微信已启动并登录后再试", "warning")
            return

        self._log_message(f"微信状态检查成功: {status_msg}", "success")

        contact_input = self.contact_var.get().strip()
        if not contact_input:
            self._log_message("请先输入微信联系人名称", "error")
            return

        # 解析联系人列表（支持分号分隔的多人模式）
        contacts = self._parse_contacts(contact_input)

        self._log_message(f"开始测试微信连接，共{len(contacts)}个联系人...", "info")

        success_count = 0
        for i, contact in enumerate(contacts, 1):
            try:
                self._log_message(f"正在测试联系人 {i}/{len(contacts)}: {contact}", "info")
                # 简化测试，直接尝试发送测试消息
                test_message = f"微信连接测试 - {datetime.now().strftime('%H:%M:%S')}"
                success = send_logistics_message_to_wechat(contact, test_message)
                if success:
                    self._log_message(f"联系人 '{contact}' 连接成功！", "success")
                    success_count += 1
                else:
                    self._log_message(f"联系人 '{contact}' 连接失败", "error")
            except Exception as e:
                self._log_message(f"联系人 '{contact}' 连接失败: {str(e)}", "error")

        if success_count == len(contacts):
            self._log_message(f"所有联系人连接成功！({success_count}/{len(contacts)})", "success")
        elif success_count > 0:
            self._log_message(f"部分联系人连接成功 ({success_count}/{len(contacts)})", "warning")
        else:
            self._log_message("所有联系人连接失败", "error")

    def _parse_contacts(self, contact_input):
        """解析联系人输入，支持多人模式"""
        if not contact_input:
            return []

        # 使用分号分隔多个联系人
        contacts = [contact.strip() for contact in contact_input.split(';')]
        # 过滤空字符串
        contacts = [contact for contact in contacts if contact]

        return contacts

    def _browse_file(self):
        """打开文件选择对话框"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if file_path:
            old_path = self.file_path.get()
            self.file_path.set(file_path)
            self._log_message(f"已选择文件: {file_path}")

            # 如果正在监控且文件路径发生变化，提示用户
            if self.monitoring and old_path != file_path:
                self._log_message("文件路径已更换，监控将自动切换到新文件", "info")
                # 保存最后修改时间，确保新文件路径被正确监控
                self._save_last_file_path(file_path)

    def _save_last_file_path(self, file_path):
        """保存最后使用的文件路径"""
        try:
            with open("last_file_path.txt", "w", encoding="utf-8") as f:
                f.write(file_path)
        except Exception as e:
            self._log_message(f"保存文件路径失败: {str(e)}", "warning")

    def _load_last_file_path(self):
        """加载最后使用的文件路径"""
        try:
            if os.path.exists("last_file_path.txt"):
                with open("last_file_path.txt", "r", encoding="utf-8") as f:
                    return f.read().strip()
        except Exception as e:
            self._log_message(f"加载文件路径失败: {str(e)}", "warning")
        return "旺店通出库数据.xlsx"  # 默认文件名
    
    def _start_monitoring(self):
        """开始监控文件"""
        file_path = self.file_path.get().strip()

        # 如果文件不存在，仍然开始监控（等待文件创建）
        if not os.path.exists(file_path):
            self._log_message(f"文件暂不存在，将等待文件创建: {file_path}", "warning")

        self.monitoring = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_text.set("正在监控")

        # 更新状态指示器
        self.status_indicator.config(text="启动中", foreground="#FF8800")

        self._log_message(f"开始监控文件: {file_path}", "info")

        # 在新线程中启动监控
        self.monitor_thread = threading.Thread(target=self._monitor_excel_file)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def _stop_monitoring(self):
        """停止监控文件"""
        self.monitoring = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_text.set("已停止")
        self.status_indicator.config(text="已停止", foreground="#666666")
        self._log_message("已停止监控", "info")
    
    def _process_now(self):
        """立即处理数据"""
        file_path = self.file_path.get()
        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"找不到文件: {file_path}")
            return
        
        try:
            self._log_message("开始处理数据...")
            self.status_text.set("正在处理")
            
            # 在新线程中处理数据，避免界面卡顿
            process_thread = threading.Thread(target=self._process_data_thread, args=(file_path,))
            process_thread.daemon = True
            process_thread.start()
        except Exception as e:
            self._log_message(f"处理出错: {str(e)}")
            self.status_text.set("处理出错")
            messagebox.showerror("错误", f"处理数据时出错: {str(e)}")
    
    def _process_data_thread(self, file_path):
        """在线程中处理数据"""
        try:
            output_file, logistics_stats = create_pivot_table(file_path)
            # 在主线程中更新UI
            self.after(0, lambda: self._log_message("数据处理完成！", "success"))
            self.after(0, lambda: self.status_text.set("就绪"))
            self.after(0, lambda: self.last_update_time.set(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

            # 显示物流统计信息
            self.after(0, lambda: self._log_message(f"物流统计: {logistics_stats['message']}", "info"))

            # 如果启用了自动发送，则发送文件到微信
            if self.auto_send_enabled.get():
                self.after(0, lambda: self._send_file_to_wechat(output_file, logistics_stats))
            else:
                # 只有在未启用自动发送时才弹出提示框
                self.after(0, lambda: self._log_message("数据处理完成！可在输出目录查看结果文件。", "success"))
        except Exception as e:
            # 在主线程中更新UI
            self.after(0, lambda: self._log_message(f"处理出错: {str(e)}", "error"))
            self.after(0, lambda: self.status_text.set("处理出错"))
            self.after(0, lambda: messagebox.showerror("错误", f"处理数据时出错: {str(e)}"))

    def _send_file_to_wechat(self, file_path, logistics_stats=None):
        """发送文件到微信（支持多人模式）"""
        if not WECHAT_AVAILABLE:
            self._log_message("微信发送功能不可用，跳过发送", "warning")
            messagebox.showinfo("成功", "数据处理完成！（微信发送功能不可用）")
            return

        # 检查微信登录状态
        self._log_message("正在检查微信登录状态...", "info")
        is_logged_in, status_msg = check_wechat_login_status()

        if not is_logged_in:
            self._log_message(f"微信状态检查失败: {status_msg}", "error")
            self._log_message("跳过微信发送，请确保微信已启动并登录", "warning")
            messagebox.showinfo("成功", f"数据处理完成！\n微信发送失败: {status_msg}")
            return

        self._log_message(f"微信状态检查成功: {status_msg}", "success")

        contact_input = self.contact_var.get().strip()
        if not contact_input:
            self._log_message("未设置微信联系人，跳过发送", "warning")
            messagebox.showinfo("成功", "数据处理完成！（未发送到微信）")
            return

        # 解析联系人列表
        contacts = self._parse_contacts(contact_input)
        self._log_message(f"准备发送到{len(contacts)}个联系人: {', '.join(contacts)}", "info")

        try:
            # 根据用户选择决定发送格式
            if self.send_as_image.get() and EXCEL_TO_IMAGE_AVAILABLE:
                self._log_message("正在转换Excel为图片...", "info")
                self.status_text.set("正在转换")

                # 在新线程中转换并发送图片（包含物流统计消息）
                convert_thread = threading.Thread(target=self._convert_and_send_image_to_multiple, args=(file_path, contacts, logistics_stats))
                convert_thread.daemon = True
                convert_thread.start()
            else:
                self._log_message("正在发送Excel文件到微信...", "info")
                self.status_text.set("正在发送")

                # 在新线程中发送文件，避免界面卡顿
                send_thread = threading.Thread(target=self._send_file_to_multiple, args=(file_path, contacts, logistics_stats))
                send_thread.daemon = True
                send_thread.start()
        except Exception as e:
            self._log_message(f"发送准备失败: {str(e)}", "error")
            self.status_text.set("就绪")

    def _send_file_to_multiple(self, file_path, contacts, logistics_stats=None):
        """发送文件到多个联系人（支持重试机制）"""
        success_count = 0
        total_count = len(contacts)

        self.after(0, lambda: self._log_message(f"开始发送到{total_count}个联系人，支持自动重试机制", "info"))

        for i, contact in enumerate(contacts, 1):
            try:
                self.after(0, lambda c=contact, idx=i, total=total_count:
                          self._log_message(f"正在发送到联系人 {idx}/{total}: {c}", "info"))

                self.after(0, lambda idx=i, total=total_count:
                          self.status_text.set(f"发送中 ({idx}/{total})"))

                # 发送物流统计消息（支持重试）
                if logistics_stats:
                    self.after(0, lambda c=contact:
                              self._log_message(f"发送物流统计消息到: {c}", "info"))
                    msg_success = send_logistics_message_to_wechat(contact, logistics_stats['message'])
                    if msg_success:
                        self.after(0, lambda c=contact:
                                  self._log_message(f"✅ 物流统计消息发送成功: {c}", "success"))
                    else:
                        self.after(0, lambda c=contact:
                                  self._log_message(f"❌ 物流统计消息发送失败: {c}", "error"))

                # 发送文件（支持重试）
                self.after(0, lambda c=contact:
                          self._log_message(f"发送Excel文件到: {c}", "info"))
                file_success = send_file_to_wechat_simple(contact, file_path)
                if file_success:
                    self.after(0, lambda c=contact:
                              self._log_message(f"✅ Excel文件发送成功: {c}", "success"))
                    success_count += 1
                else:
                    self.after(0, lambda c=contact:
                              self._log_message(f"❌ Excel文件发送失败: {c}", "error"))

            except Exception as e:
                self.after(0, lambda c=contact, err=str(e):
                          self._log_message(f"❌ 发送到 {c} 异常: {err}", "error"))

        # 发送完成总结
        if success_count == total_count:
            self.after(0, lambda: self._log_message(f"🎉 所有联系人发送成功！({success_count}/{total_count})", "success"))
            self.after(0, lambda: self.status_text.set("发送完成"))
        elif success_count > 0:
            self.after(0, lambda: self._log_message(f"⚠️ 部分联系人发送成功 ({success_count}/{total_count})", "warning"))
            self.after(0, lambda: self.status_text.set("部分成功"))
        else:
            self.after(0, lambda: self._log_message("❌ 所有联系人发送失败", "error"))
            self.after(0, lambda: self.status_text.set("发送失败"))

        # 3秒后恢复就绪状态
        self.after(3000, lambda: self.status_text.set("就绪"))

    def _convert_and_send_image_to_multiple(self, file_path, contacts, logistics_stats=None):
        """转换并发送图片到多个联系人"""
        import pythoncom

        # 在当前线程中初始化COM
        try:
            pythoncom.CoInitialize()
            com_initialized = True
        except:
            com_initialized = False

        try:
            # 先转换Excel为图片
            self.after(0, lambda: self._log_message("正在转换Excel为图片...", "info"))

            try:
                image_path = excel_to_image_simple(file_path)
                self.after(0, lambda: self._log_message("图片转换成功", "success"))
            except Exception as e1:
                self.after(0, lambda: self._log_message(f"简化方法失败，尝试高级方法: {str(e1)}", "warning"))
                try:
                    image_path = excel_to_image(file_path)
                    self.after(0, lambda: self._log_message("使用高级方法转换成功", "success"))
                except Exception as e2:
                    raise Exception(f"所有转换方法都失败: 简化方法={str(e1)}, 高级方法={str(e2)}")

            # 发送到多个联系人
            success_count = 0
            total_count = len(contacts)

            for i, contact in enumerate(contacts, 1):
                try:
                    self.after(0, lambda c=contact, idx=i, total=total_count:
                              self._log_message(f"正在发送图片到联系人 {idx}/{total}: {c}", "info"))

                    # 发送物流统计消息
                    if logistics_stats:
                        msg_success = send_logistics_message_to_wechat(contact, logistics_stats['message'])
                        if msg_success:
                            self.after(0, lambda c=contact:
                                      self._log_message(f"物流统计消息发送成功: {c}", "success"))
                        else:
                            self.after(0, lambda c=contact:
                                      self._log_message(f"物流统计消息发送失败: {c}", "error"))

                    # 发送图片
                    img_success = send_file_to_wechat_simple(contact, image_path)
                    if img_success:
                        self.after(0, lambda c=contact:
                                  self._log_message(f"图片发送成功: {c}", "success"))
                        success_count += 1
                    else:
                        self.after(0, lambda c=contact:
                                  self._log_message(f"图片发送失败: {c}", "error"))

                except Exception as e:
                    self.after(0, lambda c=contact, err=str(e):
                              self._log_message(f"发送图片到 {c} 失败: {err}", "error"))

            # 发送完成总结
            if success_count == total_count:
                self.after(0, lambda: self._log_message(f"所有联系人图片发送成功！({success_count}/{total_count})", "success"))
            elif success_count > 0:
                self.after(0, lambda: self._log_message(f"部分联系人图片发送成功 ({success_count}/{total_count})", "warning"))
            else:
                self.after(0, lambda: self._log_message("所有联系人图片发送失败", "error"))

        except Exception as e:
            self.after(0, lambda: self._log_message(f"图片转换或发送失败: {str(e)}", "error"))
        finally:
            # 清理COM
            if com_initialized:
                try:
                    pythoncom.CoUninitialize()
                except:
                    pass
            self.after(0, lambda: self.status_text.set("就绪"))

    def _convert_and_send_image_optimized(self, file_path, contact, logistics_stats=None):
        """优化的图片转换和发送方法"""
        import pythoncom

        # 在当前线程中初始化COM
        try:
            pythoncom.CoInitialize()
            com_initialized = True
        except:
            com_initialized = False

        try:
            # 步骤1: 先发送物流统计消息
            if logistics_stats:
                self.after(0, lambda: self._log_message("正在发送物流统计消息...", "info"))
                try:
                    success = send_logistics_message_to_wechat(contact, logistics_stats['message'])
                    if success:
                        self.after(0, lambda: self._log_message(f"物流统计消息发送成功: {logistics_stats['message']}", "success"))
                    else:
                        self.after(0, lambda: self._log_message("物流统计消息发送失败", "error"))
                except Exception as e:
                    self.after(0, lambda: self._log_message(f"发送物流统计消息出错: {str(e)}", "error"))

            # 步骤2: 转换Excel为图片
            self.after(0, lambda: self._log_message("正在转换Excel为图片...", "info"))

            try:
                image_path = excel_to_image_simple(file_path)
                self.after(0, lambda: self._log_message("图片转换成功", "success"))
            except Exception as e1:
                self.after(0, lambda: self._log_message(f"简化方法失败，尝试高级方法: {str(e1)}", "warning"))
                try:
                    image_path = excel_to_image(file_path)
                    self.after(0, lambda: self._log_message("使用高级方法转换成功", "success"))
                except Exception as e2:
                    raise Exception(f"所有转换方法都失败: 简化方法={str(e1)}, 高级方法={str(e2)}")

            # 步骤3: 发送图片
            self.after(0, lambda: self._log_message("正在发送图片到微信...", "info"))
            self.after(0, lambda: self.status_text.set("正在发送"))

            try:
                success = send_file_to_wechat_simple(contact, image_path)
                if success:
                    self.after(0, lambda: self._log_message("图片发送成功", "success"))
                    self.after(0, lambda: self._log_message("数据处理完成并已发送表格图片到微信！", "success"))
                else:
                    self.after(0, lambda: self._log_message("图片发送失败", "error"))
                self.after(0, lambda: self.status_text.set("就绪"))
            except Exception as e:
                self.after(0, lambda: self._log_message(f"图片发送失败: {str(e)}", "error"))
                self.after(0, lambda: self.status_text.set("就绪"))

        except Exception as e:
            self.after(0, lambda: self._log_message(f"图片转换或发送失败: {str(e)}", "error"))
            self.after(0, lambda: self.status_text.set("就绪"))
        finally:
            # 清理COM
            if com_initialized:
                try:
                    pythoncom.CoUninitialize()
                except:
                    pass

    def _send_file_and_message_thread(self, file_path, contact, logistics_stats=None):
        """在线程中发送文件和消息"""
        import pythoncom

        # 在当前线程中初始化COM
        try:
            pythoncom.CoInitialize()
            com_initialized = True
        except:
            com_initialized = False

        try:
            # 步骤1: 先发送物流统计消息
            if logistics_stats:
                self.after(0, lambda: self._log_message("正在发送物流统计消息...", "info"))
                try:
                    success = send_logistics_message_to_wechat(contact, logistics_stats['message'])
                    if success:
                        self.after(0, lambda: self._log_message(f"物流统计消息发送成功: {logistics_stats['message']}", "success"))
                    else:
                        self.after(0, lambda: self._log_message("物流统计消息发送失败", "error"))
                except Exception as e:
                    self.after(0, lambda: self._log_message(f"发送物流统计消息出错: {str(e)}", "error"))

            # 步骤2: 发送Excel文件
            self.after(0, lambda: self._log_message("正在发送Excel文件到微信...", "info"))

            try:
                success = send_file_to_wechat_simple(contact, file_path)
                if success:
                    self.after(0, lambda: self._log_message("Excel文件发送成功", "success"))
                    self.after(0, lambda: self._log_message("数据处理完成并已发送Excel文件到微信！", "success"))
                else:
                    self.after(0, lambda: self._log_message("Excel文件发送失败", "error"))
                self.after(0, lambda: self.status_text.set("就绪"))
            except Exception as e:
                self.after(0, lambda: self._log_message(f"Excel文件发送失败: {str(e)}", "error"))
                self.after(0, lambda: self.status_text.set("就绪"))

        except Exception as e:
            self.after(0, lambda: self._log_message(f"文件发送失败: {str(e)}", "error"))
            self.after(0, lambda: self.status_text.set("就绪"))
        finally:
            # 清理COM
            if com_initialized:
                try:
                    pythoncom.CoUninitialize()
                except:
                    pass







    def _monitor_excel_file(self):
        """监控Excel文件的变化"""
        last_modified_time = None
        last_file_path = None

        while self.monitoring:
            try:
                # 动态获取当前文件路径，支持路径更换
                current_file_path = self.file_path.get().strip()

                # 检查文件路径是否发生变化
                if current_file_path != last_file_path:
                    if last_file_path is not None:
                        self._log_message(f"文件路径已更换: {last_file_path} → {current_file_path}", "info")
                    last_file_path = current_file_path
                    last_modified_time = None  # 重置修改时间，确保新文件被正确监控
                    self._log_message(f"开始监控新文件: {current_file_path}", "info")

                if not os.path.exists(current_file_path):
                    if last_modified_time is not None:
                        self._log_message(f"找不到监控文件: {current_file_path}", "warning")
                        self._log_message("等待文件出现...", "info")
                        self.after(0, lambda: self.status_indicator.config(text="等待文件", foreground="#FF8800"))
                        last_modified_time = None
                else:
                    current_modified_time = os.path.getmtime(current_file_path)
                    
                    if current_modified_time != last_modified_time:
                        if last_modified_time is not None:
                            update_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            self._log_message(f"检测到文件更新: {update_time}", "info")

                            try:
                                # 在主线程中更新状态
                                self.after(0, lambda: self.status_text.set("正在处理"))
                                self.after(0, lambda: self.status_indicator.config(text="处理中", foreground="#FF8800"))

                                # 处理数据
                                output_file, logistics_stats = create_pivot_table(current_file_path)

                                # 在主线程中更新UI
                                self.after(0, lambda: self._log_message("数据透视表已更新", "success"))
                                self.after(0, lambda: self._log_message(f"物流统计: {logistics_stats['message']}", "info"))
                                self.after(0, lambda: self.status_text.set("正在监控"))
                                self.after(0, lambda: self.last_update_time.set(update_time))
                                self.after(0, lambda: self.status_indicator.config(text="监控中", foreground="#008800"))

                                # 如果启用了自动发送，则发送文件到微信
                                if self.auto_send_enabled.get():
                                    self.after(0, lambda: self._send_file_to_wechat(output_file, logistics_stats))
                            except Exception as e:
                                self._log_message(f"处理文件时出错: {str(e)}", "error")
                                self.after(0, lambda: self.status_text.set("监控中(上次处理出错)"))
                                self.after(0, lambda: self.status_indicator.config(text="错误", foreground="#CC0000"))
                        else:
                            # 首次检测到文件
                            self._log_message(f"开始监控文件: {current_file_path}", "success")
                            self.after(0, lambda: self.status_text.set("正在监控"))
                            self.after(0, lambda: self.status_indicator.config(text="监控中", foreground="#008800"))

                        last_modified_time = current_modified_time
            except Exception as e:
                self._log_message(f"监控过程出错: {str(e)}")
            
            time.sleep(2)  # 每2秒检查一次
    
    def _log_message(self, message, level="info"):
        """添加带颜色的日志消息
        level: info, success, error, warning
        """
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 在主线程中更新UI
        if threading.current_thread() is threading.main_thread():
            self._append_to_log(current_time, message, level)
        else:
            self.after(0, lambda: self._append_to_log(current_time, message, level))

    def _append_to_log(self, timestamp, message, level):
        """将消息追加到日志文本框"""
        self.log_text.insert(tk.END, f"[{timestamp}] ", level)
        self.log_text.insert(tk.END, f"{message}\n", level)
        self.log_text.see(tk.END)

        # 保存日志消息
        log_entry = f"[{timestamp}] {message}"
        self.log_messages.append(log_entry)
        # 限制日志消息数量
        if len(self.log_messages) > 100:
            self.log_messages.pop(0)
    
    def _on_closing(self):
        """关闭窗口时的处理"""
        # 保存联系人配置
        self._save_contact()

        if self.monitoring:
            if messagebox.askokcancel("确认退出", "监控正在进行中，确定要退出吗？"):
                self.monitoring = False
                self.destroy()
        else:
            self.destroy()

def analyze_logistics_numbers(df):
    """分析物流单号统计信息（去重处理）"""
    if '物流单号' not in df.columns:
        return {
            'sf_count': 0,
            'jd_count': 0,
            'total_count': 0,
            'unique_count': 0,
            'duplicate_count': 0,
            'message': '未找到物流单号列'
        }

    # 获取物流单号列，去除空值并转换为字符串
    logistics_numbers = df['物流单号'].dropna().astype(str)

    # 原始总数（包含重复）
    original_total = len(logistics_numbers)

    # 去重处理：获取唯一的物流单号
    unique_logistics_numbers = logistics_numbers.drop_duplicates()

    # 计算重复数量
    duplicate_count = original_total - len(unique_logistics_numbers)

    # 统计SF开头的单号（顺丰快递）- 基于去重后的数据
    sf_numbers = unique_logistics_numbers[unique_logistics_numbers.str.startswith('SF')]
    sf_count = len(sf_numbers)

    # 统计JD开头的单号（京东快递）- 基于去重后的数据
    jd_numbers = unique_logistics_numbers[unique_logistics_numbers.str.startswith('JD')]
    jd_count = len(jd_numbers)

    # 唯一单号总计
    unique_total_count = len(unique_logistics_numbers)

    # 生成当前日期
    current_date = datetime.now().strftime('%Y年%m月%d日')

    # 生成微信消息（基于去重后的数据）
    message = f"{current_date}快递出货单量如下：\n顺丰快递：{sf_count}单\n京东快递：{jd_count}单\n快递总单量：{unique_total_count}单"

    return {
        'sf_count': sf_count,
        'jd_count': jd_count,
        'total_count': original_total,  # 原始总数
        'unique_count': unique_total_count,  # 去重后总数
        'duplicate_count': duplicate_count,  # 重复数量
        'message': message,
        'date': current_date,
        'sf_numbers': sf_numbers.tolist() if len(sf_numbers) <= 10 else sf_numbers.head(10).tolist(),  # 顺丰单号样本
        'jd_numbers': jd_numbers.tolist() if len(jd_numbers) <= 10 else jd_numbers.head(10).tolist()   # 京东单号样本
    }

def send_logistics_message_to_wechat(contact_name, message, timeout=15, max_retries=3):
    """发送物流统计消息到微信（支持重试机制）"""
    if not WECHAT_AVAILABLE:
        print("微信发送功能不可用")
        return False

    for attempt in range(max_retries):
        try:
            import time
            from wxauto import WeChat

            # 在每次尝试前检查微信登录状态
            print(f"第{attempt + 1}次尝试发送消息到 '{contact_name}'...")
            is_logged_in, status_msg = check_wechat_login_status()

            if not is_logged_in:
                print(f"❌ 微信状态检查失败: {status_msg}")
                if attempt < max_retries - 1:
                    print("⏳ 等待30秒后重试，请确保微信已登录...")
                    time.sleep(30)
                    continue
                else:
                    print("❌ 达到最大重试次数，发送失败")
                    return False

            print(f"✅ 微信状态正常，开始发送消息: {message}")

            # 直接发送，不使用线程避免COM问题
            wx = WeChat()
            wx.ChatWith(contact_name)
            time.sleep(1)  # 等待聊天窗口打开
            wx.SendMsg(message)

            print(f"✅ 成功发送消息到微信联系人 '{contact_name}'")
            return True

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 第{attempt + 1}次发送失败: {error_msg}")

            # 检查是否是微信登录相关的错误
            if "微信未登录" in error_msg or "登录" in error_msg:
                print("🔄 检测到微信登录问题，等待重试...")
                if attempt < max_retries - 1:
                    print("⏳ 等待30秒后重试，请重新登录微信...")
                    time.sleep(30)
                    continue

            # 提供具体的错误解决建议
            if "UIAutomationCore.dll" in error_msg:
                print("💡 解决方案: 安装Windows Update KB971513")
                return False  # 这种错误不需要重试
            elif "CoInitialize" in error_msg:
                print("💡 解决方案: 重启程序")
                return False  # 这种错误不需要重试
            elif "找不到" in error_msg or "联系人" in error_msg:
                print(f"💡 解决方案: 检查联系人名称 '{contact_name}' 是否正确")
                return False  # 联系人名称错误不需要重试
            elif "微信" in error_msg:
                print("💡 可能的解决方案: 确保微信已登录并在前台运行")
                if attempt < max_retries - 1:
                    print("⏳ 等待30秒后重试...")
                    time.sleep(30)
                    continue

            # 其他错误，如果还有重试机会就继续
            if attempt < max_retries - 1:
                print(f"⏳ 等待10秒后进行第{attempt + 2}次尝试...")
                time.sleep(10)
            else:
                print("❌ 达到最大重试次数，发送失败")
                return False

    return False

def send_file_to_wechat_simple(contact_name, file_path, max_retries=3):
    """发送文件到微信（支持重试机制）"""
    if not WECHAT_AVAILABLE:
        print("微信发送功能不可用")
        return False

    import os
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False

    for attempt in range(max_retries):
        try:
            import time
            from wxauto import WeChat

            # 在每次尝试前检查微信登录状态
            print(f"第{attempt + 1}次尝试发送文件到 '{contact_name}': {os.path.basename(file_path)}")
            is_logged_in, status_msg = check_wechat_login_status()

            if not is_logged_in:
                print(f"❌ 微信状态检查失败: {status_msg}")
                if attempt < max_retries - 1:
                    print("⏳ 等待30秒后重试，请确保微信已登录...")
                    time.sleep(30)
                    continue
                else:
                    print("❌ 达到最大重试次数，发送失败")
                    return False

            print(f"✅ 微信状态正常，开始发送文件...")

            # 直接发送，不使用线程避免COM问题
            wx = WeChat()
            wx.ChatWith(contact_name)
            time.sleep(1)  # 等待聊天窗口打开
            wx.SendFiles(file_path)

            print(f"✅ 成功发送文件到微信联系人 '{contact_name}'")
            return True

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 第{attempt + 1}次发送失败: {error_msg}")

            # 检查是否是微信登录相关的错误
            if "微信未登录" in error_msg or "登录" in error_msg:
                print("🔄 检测到微信登录问题，等待重试...")
                if attempt < max_retries - 1:
                    print("⏳ 等待30秒后重试，请重新登录微信...")
                    time.sleep(30)
                    continue

            # 提供具体的错误解决建议
            if "UIAutomationCore.dll" in error_msg:
                print("💡 解决方案: 安装Windows Update KB971513")
                return False  # 这种错误不需要重试
            elif "CoInitialize" in error_msg:
                print("💡 解决方案: 重启程序")
                return False  # 这种错误不需要重试
            elif "找不到" in error_msg or "联系人" in error_msg:
                print(f"💡 解决方案: 检查联系人名称 '{contact_name}' 是否正确")
                return False  # 联系人名称错误不需要重试
            elif "微信" in error_msg:
                print("💡 可能的解决方案: 确保微信已登录并在前台运行")
                if attempt < max_retries - 1:
                    print("⏳ 等待30秒后重试...")
                    time.sleep(30)
                    continue

            # 其他错误，如果还有重试机会就继续
            if attempt < max_retries - 1:
                print(f"⏳ 等待10秒后进行第{attempt + 2}次尝试...")
                time.sleep(10)
            else:
                print("❌ 达到最大重试次数，发送失败")
                return False

    return False

# 保留原有的数据处理函数
def create_pivot_table(file_path, max_retries=3, retry_delay=2):
    # 读取Excel文件，添加重试机制
    for attempt in range(max_retries):
        try:
            df = pd.read_excel(file_path)
            break
        except PermissionError:
            if attempt < max_retries - 1:
                print(f'文件被占用，{retry_delay}秒后重试...')
                time.sleep(retry_delay)
            else:
                raise Exception('文件持续被占用，请确保Excel文件未被其他程序打开')

    # 统计物流单号
    logistics_stats = analyze_logistics_numbers(df)

    # 创建数据透视表
    pivot = pd.pivot_table(
        df,
        index=['货主'],
        columns=['分类'],
        values=['货品数量'],
        aggfunc='sum',
        margins=True,  # 添加总计
        margins_name='总计'
    )
    
    # 格式化数值，不保留小数
    for col in pivot.columns:
        pivot[col] = pivot[col].map(lambda x: int(round(x)) if pd.notnull(x) else x)
    
    # 处理列标题，移除多级索引
    pivot.columns = pivot.columns.get_level_values(1)
    
    # 生成新的Excel文件名（包含当前日期）
    current_date = datetime.now().strftime('%Y-%m-%d')
    output_file = f'{current_date}线上出货数据汇总.xlsx'
    
    # 创建ExcelWriter对象
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入数据透视表
        pivot.to_excel(writer, sheet_name='Sheet1', startrow=1)
        
        # 获取工作表对象
        worksheet = writer.sheets['Sheet1']
        
        # 设置标题
        title = f'{current_date}线上出货数据汇总'
        max_col = worksheet.max_column
        worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=max_col)
        title_cell = worksheet.cell(row=1, column=1, value=title)
        title_cell.font = Font(name='微软雅黑', size=18, bold=True, color='000080')
        title_cell.alignment = Alignment(horizontal='center', vertical='center')
        title_cell.fill = PatternFill(start_color='DCE6F1', end_color='E6F2FF', fill_type='solid')
        
        # 设置行高
        worksheet.row_dimensions[1].height = 30
        
        # 设置列宽
        for col in worksheet.columns:
            max_length = 0
            try:
                column = col[0].column_letter
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 4)  # 增加宽度，使表格更加宽松
                worksheet.column_dimensions[column].width = adjusted_width
            except AttributeError:
                # 跳过合并单元格，它们没有column_letter属性
                pass
            
        # 设置第一列稍宽一些，以便更好地显示索引
        first_col = get_column_letter(1)
        worksheet.column_dimensions[first_col].width = worksheet.column_dimensions[first_col].width + 2
        
        # 设置边框和对齐方式
        header_border = Border(
            left=Side(style='medium', color='000000'),
            right=Side(style='medium', color='000000'),
            top=Side(style='medium', color='000000'),
            bottom=Side(style='medium', color='000000')
        )
        
        data_border = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )
        
        # 设置表格整体样式
        for row in worksheet.iter_rows(min_row=2):
            for cell in row:
                # 确保每个单元格都有边框
                cell.border = data_border
                
                # 设置对齐方式：所有内容居中对齐
                cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 设置数字格式（对数字类型应用千分位格式，不保留小数）
                try:
                    if isinstance(cell.value, (int, float)) or (isinstance(cell.value, str) and cell.value.replace('.', '', 1).isdigit()):
                        cell.number_format = '#,##0'
                except:
                    pass
                
                # 设置行高
                worksheet.row_dimensions[cell.row].height = 22
        
        # 设置表头样式
        for cell in worksheet[2]:
            cell.font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='366092', end_color='4472C4', fill_type='solid')
            cell.border = header_border
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            # 设置表头单元格的内边距
            worksheet.row_dimensions[2].height = 25
        
        # 设置总计行和总计列的样式
        last_row = worksheet.max_row
        last_col = worksheet.max_column
        
        # 设置总计行样式
        for col in range(1, last_col + 1):
            cell = worksheet.cell(row=last_row, column=col)
            cell.font = Font(name='微软雅黑', bold=True, color='000080', size=11)
            cell.fill = PatternFill(start_color='DCE6F1', end_color='E6F2FF', fill_type='solid')
            cell.border = header_border
        
        # 设置总计列样式
        for row in range(3, last_row + 1):
            cell = worksheet.cell(row=row, column=last_col)
            cell.font = Font(name='微软雅黑', bold=True, color='000080', size=11)
            cell.fill = PatternFill(start_color='DCE6F1', end_color='E6F2FF', fill_type='solid')
            cell.border = header_border
        
        # 设置总计交叉单元格特殊样式
        total_cell = worksheet.cell(row=last_row, column=last_col)
        total_cell.font = Font(name='微软雅黑', bold=True, color='000080', size=12)
        total_cell.fill = PatternFill(start_color='BDD7EE', end_color='C5D9F1', fill_type='solid')
        total_cell.border = header_border
        total_cell.alignment = Alignment(horizontal='center', vertical='center')

    return output_file, logistics_stats

# 程序入口
if __name__ == '__main__':
    app = WangdiantongApp()
    # 添加联系人变化的跟踪
    def on_contact_change(*args):
        app._save_contact()
    app.contact_var.trace_add("write", on_contact_change)
    app.mainloop()