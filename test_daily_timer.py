#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
每日定时功能测试脚本
用于验证每日定时执行逻辑的正确性
"""

from datetime import datetime, timedelta
import time

def get_next_daily_run_time(daily_times):
    """
    获取下次每日执行时间
    
    Args:
        daily_times: 时间列表，格式如 ["09:00", "14:00", "18:00"]
    
    Returns:
        datetime: 下次执行时间
    """
    if not daily_times:
        return None
    
    now = datetime.now()
    today = now.date()
    
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"设置的执行时间: {', '.join(daily_times)}")
    
    # 检查今天是否还有未执行的时间
    for time_str in daily_times:
        hour, minute = map(int, time_str.split(':'))
        run_time = datetime.combine(today, datetime.min.time().replace(hour=hour, minute=minute))
        
        print(f"  检查时间 {time_str}: {run_time.strftime('%Y-%m-%d %H:%M:%S')}", end="")
        
        if run_time > now:
            print(" ✅ 今天可执行")
            return run_time
        else:
            print(" ❌ 已过时")
    
    # 如果今天没有了，返回明天的第一个时间
    tomorrow = today + timedelta(days=1)
    first_time = daily_times[0]
    hour, minute = map(int, first_time.split(':'))
    next_run = datetime.combine(tomorrow, datetime.min.time().replace(hour=hour, minute=minute))
    
    print(f"  今天所有时间已过，下次执行: 明天 {first_time}")
    return next_run

def test_scenarios():
    """测试各种时间场景"""
    
    # 测试场景1: 工作时间
    print("=" * 60)
    print("测试场景1: 工作时间执行")
    print("=" * 60)
    
    daily_times = ["09:00", "12:00", "15:00", "18:00"]
    next_run = get_next_daily_run_time(daily_times)
    
    if next_run:
        wait_seconds = (next_run - datetime.now()).total_seconds()
        print(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"等待时间: {wait_seconds/3600:.2f} 小时")
    
    print()
    
    # 测试场景2: 高频监控
    print("=" * 60)
    print("测试场景2: 高频监控")
    print("=" * 60)
    
    daily_times = ["08:00", "10:00", "12:00", "14:00", "16:00", "18:00", "20:00"]
    next_run = get_next_daily_run_time(daily_times)
    
    if next_run:
        wait_seconds = (next_run - datetime.now()).total_seconds()
        print(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"等待时间: {wait_seconds/60:.0f} 分钟")
    
    print()
    
    # 测试场景3: 夜间执行
    print("=" * 60)
    print("测试场景3: 夜间执行")
    print("=" * 60)
    
    daily_times = ["23:30"]
    next_run = get_next_daily_run_time(daily_times)
    
    if next_run:
        wait_seconds = (next_run - datetime.now()).total_seconds()
        print(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"等待时间: {wait_seconds/3600:.1f} 小时")
    
    print()

def simulate_daily_timer():
    """模拟每日定时器运行"""
    print("=" * 60)
    print("模拟每日定时器运行 (按Ctrl+C停止)")
    print("=" * 60)
    
    daily_times = ["09:00", "14:00", "18:00"]
    
    try:
        while True:
            next_run = get_next_daily_run_time(daily_times)
            
            if next_run:
                wait_seconds = (next_run - datetime.now()).total_seconds()
                
                print(f"\n⏰ 下次执行: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
                
                if wait_seconds > 3600:
                    print(f"⏳ 等待时间: {wait_seconds/3600:.1f} 小时")
                elif wait_seconds > 60:
                    print(f"⏳ 等待时间: {wait_seconds/60:.0f} 分钟")
                else:
                    print(f"⏳ 等待时间: {wait_seconds:.0f} 秒")
                
                # 模拟等待（实际中会是真实等待）
                if wait_seconds <= 10:
                    print("🚀 即将执行...")
                    time.sleep(2)
                    print("✅ 执行完成！")
                    time.sleep(1)
                else:
                    print("💤 等待中... (模拟)")
                    time.sleep(5)  # 模拟等待
            else:
                print("❌ 没有设置执行时间")
                break
                
    except KeyboardInterrupt:
        print("\n\n⏹️ 模拟停止")

def test_time_management():
    """测试时间管理功能"""
    print("=" * 60)
    print("测试时间管理功能")
    print("=" * 60)
    
    daily_times = []
    
    # 添加时间
    times_to_add = ["09:00", "14:00", "18:00", "09:00", "12:00"]  # 包含重复时间
    
    for time_str in times_to_add:
        print(f"添加时间: {time_str}", end="")
        if time_str not in daily_times:
            daily_times.append(time_str)
            daily_times.sort()
            print(" ✅ 成功")
        else:
            print(" ❌ 已存在")
    
    print(f"\n当前时间列表: {daily_times}")
    
    # 删除时间
    time_to_remove = "14:00"
    print(f"\n删除时间: {time_to_remove}", end="")
    if time_to_remove in daily_times:
        daily_times.remove(time_to_remove)
        print(" ✅ 成功")
    else:
        print(" ❌ 不存在")
    
    print(f"删除后时间列表: {daily_times}")

def main():
    """主函数"""
    print("🕐 每日定时功能测试")
    print("=" * 60)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试各种时间场景")
        print("2. 模拟每日定时器运行")
        print("3. 测试时间管理功能")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            test_scenarios()
        elif choice == "2":
            simulate_daily_timer()
        elif choice == "3":
            test_time_management()
        elif choice == "4":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
