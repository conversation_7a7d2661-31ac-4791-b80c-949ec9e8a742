#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导出销售出库明细到指定路径
将货主名称、货品编号、货品数量、备注写入到指定Excel文件的ABCDE列
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient

class GoodsArchiveMapper:
    """货品档案映射器"""
    
    def __init__(self, archive_file: str = "货品档案.xlsx"):
        """
        初始化货品档案映射器
        
        Args:
            archive_file: 货品档案Excel文件路径
        """
        self.archive_file = archive_file
        self.goods_map = {}
        self.load_goods_archive()
    
    def load_goods_archive(self):
        """加载货品档案数据"""
        try:
            from openpyxl import load_workbook
            
            print(f"📋 正在加载货品档案: {self.archive_file}")
            
            # 读取Excel文件
            wb = load_workbook(self.archive_file, read_only=True)
            ws = wb.active
            
            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(str(cell.value).strip())
                else:
                    headers.append('')
            
            print(f"📋 字段列表: {headers}")
            
            # 查找关键字段的索引
            goods_name_idx = None
            goods_no_idx = None
            goods_remark_idx = None
            
            # 尝试不同的字段名称组合
            goods_name_fields = ['货品名称', '商品名称', '产品名称', 'goods_name', 'product_name']
            goods_no_fields = ['货品编号', '商品编号', '产品编号', 'goods_no', 'product_no', 'goods_code']
            goods_remark_fields = ['货品备注', '商品备注', '产品备注', 'goods_remark', 'product_remark', 'remark', '备注']
            
            for i, header in enumerate(headers):
                header_lower = header.lower()
                if goods_name_idx is None:
                    for field in goods_name_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_name_idx = i
                            print(f"✅ 找到货品名称字段: {header} (列 {i+1})")
                            break
                
                if goods_no_idx is None:
                    for field in goods_no_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_no_idx = i
                            print(f"✅ 找到货品编号字段: {header} (列 {i+1})")
                            break
                
                if goods_remark_idx is None:
                    for field in goods_remark_fields:
                        if field.lower() in header_lower or header_lower in field.lower():
                            goods_remark_idx = i
                            print(f"✅ 找到货品备注字段: {header} (列 {i+1})")
                            break
            
            if goods_name_idx is None:
                print("⚠️ 未找到货品名称字段，将使用第一列作为货品名称")
                goods_name_idx = 0
            
            # 读取数据行
            row_count = 0
            for row in ws.iter_rows(min_row=2, values_only=True):
                if not row or not any(row):
                    continue
                
                # 获取货品名称
                goods_name = ''
                if goods_name_idx < len(row) and row[goods_name_idx]:
                    goods_name = str(row[goods_name_idx]).strip()
                
                if not goods_name:
                    continue
                
                # 获取货品编号
                goods_no = ''
                if goods_no_idx is not None and goods_no_idx < len(row) and row[goods_no_idx]:
                    goods_no = str(row[goods_no_idx]).strip()
                
                # 获取货品备注
                goods_remark = ''
                if goods_remark_idx is not None and goods_remark_idx < len(row) and row[goods_remark_idx]:
                    goods_remark = str(row[goods_remark_idx]).strip()
                
                self.goods_map[goods_name] = {
                    'goods_no': goods_no,
                    'goods_remark': goods_remark
                }
                row_count += 1
            
            wb.close()
            
            print(f"✅ 成功加载 {len(self.goods_map)} 条货品映射")
            
        except FileNotFoundError:
            print(f"❌ 货品档案文件未找到: {self.archive_file}")
            print("请确保文件存在于当前目录")
        except ImportError:
            print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
        except Exception as e:
            print(f"❌ 加载货品档案失败: {e}")
    
    def get_goods_info(self, goods_name: str) -> Dict[str, str]:
        """
        根据货品名称获取货品信息
        
        Args:
            goods_name: 货品名称
            
        Returns:
            包含货品编号和备注的字典
        """
        if not goods_name:
            return {'goods_no': '', 'goods_remark': ''}
        
        # 精确匹配
        if goods_name in self.goods_map:
            return self.goods_map[goods_name]
        
        # 模糊匹配
        goods_name_lower = goods_name.lower().strip()
        for name, info in self.goods_map.items():
            if goods_name_lower in name.lower() or name.lower() in goods_name_lower:
                return info
        
        return {'goods_no': '', 'goods_remark': ''}

def export_to_specific_path():
    """导出销售出库明细到指定路径"""
    
    print("🚛 导出销售出库明细到指定路径")
    print("=" * 60)
    
    # 初始化货品档案映射器
    goods_mapper = GoodsArchiveMapper()
    
    # 创建客户端
    client = WDTPostClient()
    
    # 设置当天时间范围
    today = datetime.now()
    start_time = today.replace(hour=0, minute=0, second=0, microsecond=0)
    end_time = today.replace(hour=23, minute=59, second=59, microsecond=0)
    
    if end_time > datetime.now():
        end_time = datetime.now()
    
    print(f"📅 查询日期: {today.strftime('%Y-%m-%d')}")
    print(f"⏰ 时间范围: {start_time.strftime('%H:%M:%S')} ~ {end_time.strftime('%H:%M:%S')}")
    
    # 查询参数
    base_params = {
        "start_consign_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end_consign_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "page_size": 30  # API实际每页最多返回30条
    }
    
    print(f"🔍 开始查询出库单...")
    
    all_stockouts = []
    page_no = 0
    
    # 首先获取总记录数
    try:
        first_response = client.call_api('stockout.query', base_params)
        total_records = int(first_response.get('total', 0))
        print(f"📊 API返回总记录数: {total_records}")
        
        if total_records == 0:
            print("📝 今天没有出库单数据")
            return
            
    except Exception as e:
        print(f"❌ 获取总记录数失败: {e}")
        return
    
    # 分页查询所有数据
    while True:
        try:
            # 当前页参数
            current_params = {
                **base_params,
                "page_no": page_no
            }
            
            # 调用API
            response = client.call_api('stockout.query', current_params)
            
            if not response:
                break
            
            # 获取数据
            content = response.get('content', [])
            
            # 处理数据
            page_data = []
            if isinstance(content, list):
                page_data = content
            elif isinstance(content, dict):
                page_data = content.get('content', []) or content.get('stockouts', [])
            
            if not page_data:
                break

            # 筛选已取消(status=5)和已发货(status=95)的数据
            filtered_data = []
            cancelled_count = 0
            shipped_count = 0
            
            for item in page_data:
                if isinstance(item, dict):
                    status = str(item.get('status', ''))
                    if status in ['5', '95']:  # 已取消或已发货状态
                        filtered_data.append(item)
                        if status == '5':
                            cancelled_count += 1
                        elif status == '95':
                            shipped_count += 1

            print(f"   📊 第{page_no + 1}页: {len(page_data)} 条 - 已取消 {cancelled_count} 条, 已发货 {shipped_count} 条")
            
            # 将所有符合条件的数据添加到结果中
            all_stockouts.extend(filtered_data)
            
            print(f"   📈 累计: 已取消/已发货 {len(all_stockouts)} 条")

            page_no += 1
            
            # 如果当前页数据少于页面大小，说明已经是最后一页
            if len(page_data) < base_params["page_size"]:
                break
            
            # 安全限制：最多查询50页
            if page_no >= 50:
                print(f"⚠️ 已查询50页，停止查询")
                break

        except Exception as e:
            print(f"❌ 第 {page_no + 1} 页查询失败: {e}")
            break
    
    print(f"\n📊 查询完成！总计获取 {len(all_stockouts)} 条已取消/已发货订单")
    
    if not all_stockouts:
        print("📝 今天没有已取消或已发货的订单")
        return
    
    # 准备写入指定路径的数据
    target_path = "C:/Users/<USER>/OneDrive/Desktop/旺店通出货数据/旺店通出库数据.xlsx"

    print(f"\n📊 开始写入到指定路径...")
    print(f"📁 目标文件: {target_path}")

    # 确保目标目录存在
    target_dir = os.path.dirname(target_path)
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"📁 创建目录: {target_dir}")

    # 检查文件是否被占用
    def is_file_locked(filepath):
        """检查文件是否被占用"""
        try:
            with open(filepath, 'a'):
                return False
        except IOError:
            return True

    # 如果文件被占用，创建备份文件名
    if os.path.exists(target_path) and is_file_locked(target_path):
        import time
        timestamp = int(time.time())
        backup_path = target_path.replace('.xlsx', f'_backup_{timestamp}.xlsx')
        print(f"⚠️ 目标文件被占用，将写入备份文件: {backup_path}")
        target_path = backup_path
    
    try:
        from openpyxl import Workbook, load_workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        # 检查文件是否存在，如果存在则加载，否则创建新文件
        if os.path.exists(target_path):
            print(f"📄 加载现有文件: {target_path}")
            wb = load_workbook(target_path)
            ws = wb.active
        else:
            print(f"📄 创建新文件: {target_path}")
            wb = Workbook()
            ws = wb.active
            
            # 写入表头到ABCDE列
            headers = ['货主名称', '货品编号', '物流单号', '货品数量', '备注']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
        
        # 找到下一个可写入的行
        next_row = ws.max_row + 1 if ws.max_row > 1 else 2
        
        print(f"📝 开始从第 {next_row} 行写入数据...")
        
        # 写入数据
        written_count = 0
        for stockout in all_stockouts:
            # 获取基础信息
            owner_name = stockout.get('owner_name', '')  # 货主名称
            logistics_no = stockout.get('logistics_no', '')  # 物流单号
            
            # 商品明细
            details_list = stockout.get('goods_detail', [])
            if details_list:
                for detail in details_list:
                    # 获取货品名称
                    goods_name = detail.get('goods_name', '')
                    
                    # 从货品档案中查找货品编号和备注
                    goods_info = goods_mapper.get_goods_info(goods_name)
                    
                    # 获取货品数量
                    goods_quantity = detail.get('num', '')
                    
                    # 获取备注（优先使用商品备注，其次使用档案备注）
                    goods_remark = detail.get('remark', '') or goods_info['goods_remark']
                    
                    # 写入ABCDE列
                    ws.cell(row=next_row, column=1, value=owner_name)  # A列：货主名称
                    ws.cell(row=next_row, column=2, value=goods_info['goods_no'])  # B列：货品编号
                    ws.cell(row=next_row, column=3, value=logistics_no)  # C列：物流单号
                    ws.cell(row=next_row, column=4, value=goods_quantity)  # D列：货品数量
                    ws.cell(row=next_row, column=5, value=goods_remark)  # E列：备注
                    
                    next_row += 1
                    written_count += 1
            else:
                # 没有商品明细的出库单，只写入基础信息
                ws.cell(row=next_row, column=1, value=owner_name)  # A列：货主名称
                ws.cell(row=next_row, column=2, value='')  # B列：货品编号
                ws.cell(row=next_row, column=3, value=logistics_no)  # C列：物流单号
                ws.cell(row=next_row, column=4, value='')  # D列：货品数量
                ws.cell(row=next_row, column=5, value='')  # E列：备注
                
                next_row += 1
                written_count += 1
        
        # 自动调整列宽
        for column in ['A', 'B', 'C', 'D', 'E']:
            max_length = 0
            for cell in ws[column]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column].width = adjusted_width
        
        # 保存文件
        wb.save(target_path)
        
        print(f"✅ 写入成功！")
        print(f"📊 总计写入: {written_count} 条记录")
        print(f"📁 文件路径: {target_path}")
        print(f"📋 写入列: A(货主名称), B(货品编号), C(物流单号), D(货品数量), E(备注)")
        
        print(f"\n🎉 数据写入完成！")
        
    except ImportError:
        print("❌ 缺少openpyxl库，请先安装: pip install openpyxl")
    except Exception as e:
        print(f"❌ 写入失败: {e}")

if __name__ == "__main__":
    export_to_specific_path()
